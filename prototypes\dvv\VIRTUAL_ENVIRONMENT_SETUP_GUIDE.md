# Virtual Environment Setup Guide for DVV (Dolby Vision Validator)

## Current Status: ✅ FRESHLY RECREATED AND VERIFIED

The virtual environment has been completely recreated with all required dependencies. This guide provides complete setup instructions, verification steps, and troubleshooting information.

## 🎉 SETUP COMPLETED SUCCESSFULLY

✅ **Virtual Environment**: Freshly created at `prototypes\dvv\.venv`
✅ **Python Interpreter**: `D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe`
✅ **Main Dependencies**: FastAPI, uvicorn, jinja2, pydantic, python-multipart, aiofiles, python-jose, passlib
✅ **Log Processing Dependencies**: numpy, pandas, matplotlib, seaborn
✅ **Log Processor Scripts**: Both grid_comparison_plotter.py and firmware_log_visualizer.py are functional
✅ **Subprocess Integration**: log_processors.py correctly uses the virtual environment

## 🚀 Quick Start

To start using the DVV application:

```bash
# 1. Activate the virtual environment
.venv\Scripts\activate

# 2. Start the web application
python run.py

# 3. Open your browser to http://127.0.0.1:8000
```

## ✅ Quick Verification

Run this command to verify your setup:

```bash
# Activate virtual environment and check Python path
.venv\Scripts\Activate.ps1; python -c "import sys; print(f'Python: {sys.executable}')"
```

**Expected output:**
```
Python: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe
```

**Verify all packages:**
```bash
.venv\Scripts\Activate.ps1; python -c "import fastapi, uvicorn, numpy, pandas, matplotlib, seaborn; print('All packages available!')"
```

## How the Log Processor Uses Virtual Environment

The log processor in `log_processors.py` is correctly configured:

```python
# Line 443 and 549 in log_processors.py
cmd = [
    sys.executable,  # ✅ Uses current Python interpreter (virtual environment)
    str(script_path),
    # ... other arguments
]
```

When you run the web application from the virtual environment, `sys.executable` automatically resolves to the virtual environment's Python interpreter.

## Verification Steps

### 1. Check Current Environment

```bash
# Activate virtual environment (if not already active)
.venv\Scripts\activate

# Verify Python path
python -c "import sys; print(f'Python: {sys.executable}')"

# Verify packages
python -c "import numpy, pandas, matplotlib, seaborn; print('All packages available')"
```

### 2. Test Log Processor Scripts

```bash
# Test grid comparison plotter
python bin\grid_comparison_plotter.py --help

# Test firmware log visualizer  
python bin\firmware_log_visualizer.py --help
```

### 3. Run Verification Script

```bash
python verify_setup.py
```

### 4. Test Web Application

```bash
# Start the web application
python run.py

# Or using uvicorn directly
uvicorn main:app --reload
```

## Troubleshooting

### If Virtual Environment is Broken

If you encounter issues with the virtual environment, recreate it:

**Option 1: Use the automated script**
```bash
python fix_venv.py
```

**Option 2: Use the batch script**
```bash
recreate_venv.bat
```

**Option 3: Manual recreation**
```bash
# Remove existing virtual environment
rmdir /s /q .venv

# Create new virtual environment
python -m venv .venv

# Activate it
.venv\Scripts\activate

# Install requirements
pip install -r requirements.txt
pip install -r bin\requirements.txt
```

### If Packages are Missing

Install missing packages:

```bash
# Main application packages
pip install fastapi uvicorn jinja2 pydantic python-multipart aiofiles

# Log processing packages
pip install numpy pandas matplotlib seaborn
```

### If Scripts Don't Work

Check that the bin directory scripts exist:

```bash
dir bin\*.py
```

Expected files:
- `grid_comparison_plotter.py`
- `firmware_log_visualizer.py`

## How to Start the Application Correctly

### Method 1: Using run.py (Recommended)

```bash
# Activate virtual environment
.venv\Scripts\activate

# Start application
python run.py
```

### Method 2: Using uvicorn directly

```bash
# Activate virtual environment
.venv\Scripts\activate

# Start with uvicorn
uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### Method 3: Using Python module

```bash
# Activate virtual environment
.venv\Scripts\activate

# Start as module
python -m uvicorn main:app --reload
```

## Key Points

1. **Always activate the virtual environment** before starting the web application
2. **The log processor automatically inherits** the Python interpreter from the web application
3. **No additional configuration needed** - the current setup is correct
4. **Both log processing scripts work** with the virtual environment

## Environment Variables

The application respects these environment variables:

- `UPLOAD_DIR`: Custom upload directory
- `HOST`: Server host (default: 127.0.0.1)
- `PORT`: Server port (default: 8000)

Example:
```bash
set UPLOAD_DIR=D:\MyUploads
python run.py --host 0.0.0.0 --port 8080
```

## Files Created for Verification

- `test_python_env.py` - Basic environment testing
- `verify_setup.py` - Comprehensive verification
- `test_log_processor_subprocess.py` - Subprocess testing
- `fix_venv.py` - Automated virtual environment fix
- `recreate_venv.bat` - Windows batch script for recreation

## Conclusion

Your log processor is correctly configured to use the virtual environment. The key is to ensure you start the web application from within the activated virtual environment, and the log processor will automatically use the correct Python interpreter and packages.
