#!/usr/bin/env python3
"""
Final verification script for the recreated virtual environment
"""

import sys
import subprocess
from pathlib import Path

def main():
    print("🔍 FINAL VIRTUAL ENVIRONMENT VERIFICATION")
    print("=" * 60)
    
    # 1. Check current Python interpreter
    print("1. Python Interpreter Check:")
    print(f"   Current Python: {sys.executable}")
    expected_path = "D:\\PycharmProjects\\pq_tuning_config_editor\\prototypes\\dvv\\.venv\\Scripts\\python.exe"
    if sys.executable == expected_path:
        print("   ✅ Using correct virtual environment Python")
    else:
        print("   ❌ NOT using expected virtual environment Python")
        print(f"   Expected: {expected_path}")
        return False
    
    # 2. Check virtual environment
    print("\n2. Virtual Environment Check:")
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("   ✅ Running in virtual environment")
        print(f"   Virtual env path: {sys.prefix}")
    else:
        print("   ❌ NOT running in virtual environment")
        return False
    
    # 3. Check required packages
    print("\n3. Package Availability Check:")
    packages = {
        'fastapi': 'Main web framework',
        'uvicorn': 'ASGI server',
        'jinja2': 'Template engine',
        'pydantic': 'Data validation',
        'numpy': 'Numerical computing',
        'pandas': 'Data analysis',
        'matplotlib': 'Plotting',
        'seaborn': 'Statistical visualization'
    }
    
    all_packages_ok = True
    for package, description in packages.items():
        try:
            __import__(package)
            print(f"   ✅ {package:<12} - {description}")
        except ImportError:
            print(f"   ❌ {package:<12} - {description} (MISSING)")
            all_packages_ok = False
    
    if not all_packages_ok:
        return False
    
    # 4. Test subprocess execution (what log_processors.py does)
    print("\n4. Subprocess Execution Test:")
    cmd = [
        sys.executable,
        "-c",
        f"import sys; print('Subprocess Python: {{sys.executable}}'); import numpy, pandas, matplotlib; print('All packages available')"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✅ Subprocess execution successful")
            print(f"   Output: {result.stdout.strip()}")
        else:
            print("   ❌ Subprocess execution failed")
            print(f"   Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Subprocess execution error: {e}")
        return False
    
    # 5. Test log processor scripts
    print("\n5. Log Processor Scripts Test:")
    current_dir = Path(__file__).parent
    bin_dir = current_dir / "bin"
    
    scripts = ["grid_comparison_plotter.py", "firmware_log_visualizer.py"]
    
    for script_name in scripts:
        script_path = bin_dir / script_name
        if not script_path.exists():
            print(f"   ❌ {script_name} - File not found")
            return False
        
        # Test that the script can be imported/executed
        cmd = [sys.executable, str(script_path), "--help"]
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ {script_name} - Executable")
            else:
                print(f"   ❌ {script_name} - Execution failed")
                return False
        except Exception as e:
            print(f"   ❌ {script_name} - Error: {e}")
            return False
    
    # 6. Test log_processors.py integration
    print("\n6. Log Processor Integration Test:")
    try:
        from log_processors import SubprocessLogProcessor
        processor = SubprocessLogProcessor()
        bin_dir = processor.get_bin_directory()
        print(f"   ✅ Log processor module imported successfully")
        print(f"   ✅ Bin directory: {bin_dir}")
        
        if bin_dir.exists():
            print("   ✅ Bin directory exists")
        else:
            print("   ❌ Bin directory does not exist")
            return False
            
    except Exception as e:
        print(f"   ❌ Log processor integration failed: {e}")
        return False
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("✅ Virtual environment is correctly configured")
    print("✅ All required packages are installed")
    print("✅ Log processor scripts are functional")
    print("✅ Subprocess calls will use the virtual environment")
    print("\n📋 Next Steps:")
    print("1. Start the web application: python run.py")
    print("2. Test log processing through the web interface")
    print("3. Upload a log file and verify processing works")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ VERIFICATION FAILED")
        print("Please check the issues above and retry.")
        sys.exit(1)
    else:
        print("\n🚀 Ready to use!")
