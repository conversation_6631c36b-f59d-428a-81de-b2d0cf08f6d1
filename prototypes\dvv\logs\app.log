2025-07-23 17:45:00,090 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_DD_int3.cfg (2.2 KB) for user Ethan
2025-07-23 17:45:00,091 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_174500_Sample_TV_IDK_5.0_GD_DD_int3.cfg for user Ethan
2025-07-23 17:45:00,096 - main - INFO - Analysis complete. Found 0 undefined parameters
2025-07-23 17:45:00,098 - main - INFO - File processing complete: Analysis complete. Found 0 undefined parameter(s).
2025-07-23 17:46:13,284 - main - INFO - Processing file test_files\sample_config.txt for user test_user
2025-07-23 17:46:13,287 - main - INFO - Analysis complete. Found 21 undefined parameters
2025-07-23 18:40:06,877 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\dolby_vision_sample.cfg
2025-07-23 18:40:06,973 - main - INFO - Processing file test_files\sample_config.txt for user test_user
2025-07-23 18:40:06,977 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:40:06,977 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\sample_config.txt
2025-07-23 18:40:07,012 - main - WARNING - External validation tool failed or not available
2025-07-23 18:40:07,012 - main - INFO - Analysis complete. Found 21 undefined parameter(s).
2025-07-23 18:40:07,014 - main - INFO - Processing file test_files\dolby_vision_sample.cfg for user test_user
2025-07-23 18:40:07,016 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:40:07,017 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\dolby_vision_sample.cfg
2025-07-23 18:40:07,059 - main - WARNING - External validation tool failed or not available
2025-07-23 18:40:07,059 - main - INFO - Analysis complete. Found 9 undefined parameter(s).
2025-07-23 18:40:32,114 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\dolby_vision_sample.cfg
2025-07-23 18:40:32,158 - main - INFO - Processing file test_files\sample_config.txt for user test_user
2025-07-23 18:40:32,162 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:40:32,163 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\sample_config.txt
2025-07-23 18:40:32,199 - main - WARNING - External validation tool failed or not available
2025-07-23 18:40:32,199 - main - INFO - Analysis complete. Found 21 undefined parameter(s).
2025-07-23 18:40:32,201 - main - INFO - Processing file test_files\dolby_vision_sample.cfg for user test_user
2025-07-23 18:40:32,202 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:40:32,203 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg test_files\dolby_vision_sample.cfg
2025-07-23 18:40:32,237 - main - WARNING - External validation tool failed or not available
2025-07-23 18:40:32,238 - main - INFO - Analysis complete. Found 9 undefined parameter(s).
2025-07-23 18:41:12,450 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 18:41:12,453 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_184112_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 18:41:12,459 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:41:12,460 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_184112_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg
2025-07-23 18:41:12,512 - main - WARNING - External validation tool failed or not available
2025-07-23 18:41:12,512 - main - INFO - Analysis complete. No issues found.
2025-07-23 18:41:12,514 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-23 18:41:18,345 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 18:41:18,346 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_184118_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 18:41:18,348 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:41:18,349 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -txt_cfg D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_184118_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg
2025-07-23 18:41:18,385 - main - WARNING - External validation tool failed or not available
2025-07-23 18:41:18,385 - main - INFO - Analysis complete. No issues found.
2025-07-23 18:41:18,386 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-23 18:56:56,543 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 18:56:56,545 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_185656_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 18:56:56,547 - main - INFO - Running external DolbyVision validation tool
2025-07-23 18:56:56,547 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_185656_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_185656_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.bin
2025-07-23 18:56:56,602 - main - INFO - External tool found 1 validation warnings
2025-07-23 18:56:56,603 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-23 18:56:56,604 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-23 19:11:56,535 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-23 19:11:56,535 - __main__ - INFO - Author: Ethan Li
2025-07-23 19:11:56,535 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-23 19:12:36,859 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 19:12:36,861 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_191236_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 19:12:36,863 - main - INFO - Running external DolbyVision validation tool
2025-07-23 19:12:36,864 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_191236_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_191236_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.bin
2025-07-23 19:12:36,923 - main - INFO - External tool found 1 validation warnings
2025-07-23 19:12:36,924 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-23 19:12:36,926 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-23 22:05:50,231 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 22:05:50,235 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220550_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 22:05:50,240 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:05:50,240 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220550_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220550_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.bin
2025-07-23 22:05:50,305 - main - INFO - External tool found 1 validation warnings
2025-07-23 22:05:50,306 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-23 22:05:50,307 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-23 22:07:03,390 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 22:07:03,392 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220703_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 22:07:03,395 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:07:03,395 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220703_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_220703_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.bin
2025-07-23 22:07:03,440 - main - INFO - External tool found 1 validation warnings
2025-07-23 22:07:03,440 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-23 22:07:03,442 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-23 22:13:14,875 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-23 22:13:14,876 - __main__ - INFO - Author: Ethan Li
2025-07-23 22:13:14,876 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-23 22:14:07,922 - main - INFO - Processing upload: Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg (3.1 KB) for user Ethan
2025-07-23 22:14:07,923 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221407_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg for user Ethan
2025-07-23 22:14:07,927 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:14:07,927 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221407_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221407_Sample_TV_IDK_5.1_LS_PD_6vector_L1L4_int3.bin
2025-07-23 22:14:07,980 - main - INFO - External tool found 1 validation warnings
2025-07-23 22:14:07,980 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-23 22:14:07,981 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-23 22:19:31,982 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-23 22:19:31,984 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221931_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:19:31,986 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:19:31,986 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221931_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_221931_Wrong_PQcfg.bin
2025-07-23 22:19:32,036 - main - INFO - External tool found 5 validation warnings
2025-07-23 22:19:32,037 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-23 22:19:32,040 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-23 22:22:51,553 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-23 22:22:51,554 - __main__ - INFO - Author: Ethan Li
2025-07-23 22:22:51,554 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-23 22:23:47,604 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-23 22:23:47,606 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222347_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:23:47,609 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:23:47,610 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222347_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222347_Wrong_PQcfg.bin
2025-07-23 22:23:47,670 - main - INFO - External tool found 5 validation warnings
2025-07-23 22:23:47,671 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-23 22:23:47,673 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-23 22:24:47,252 - main - INFO - Processing upload: test_config.cfg (235.0 B) for user test_download_user
2025-07-23 22:24:47,258 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222447_test_config.cfg for user test_download_user
2025-07-23 22:24:47,263 - main - INFO - Running external DolbyVision validation tool
2025-07-23 22:24:47,263 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222447_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222447_test_config.bin
2025-07-23 22:24:47,359 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:24:47,359 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:24:47,362 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:12,708 - main - INFO - Processing upload: test_config.cfg (235.0 B) for user test_download_user
2025-07-23 22:26:12,721 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg for user test_download_user
2025-07-23 22:26:12,741 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:12,741 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.bin
2025-07-23 22:26:13,005 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:13,044 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:13,080 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:15,309 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg for user test_download_user
2025-07-23 22:26:15,317 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:15,317 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.bin
2025-07-23 22:26:15,417 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:15,417 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:17,822 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg for user test_download_user
2025-07-23 22:26:17,828 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:17,840 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222612_test_config.bin
2025-07-23 22:26:18,146 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:18,147 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:35,436 - main - INFO - Processing upload: test_config.cfg (235.0 B) for user test_download_user
2025-07-23 22:26:35,437 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg for user test_download_user
2025-07-23 22:26:35,441 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:35,441 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.bin
2025-07-23 22:26:35,564 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:35,564 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:35,566 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:37,606 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg for user test_download_user
2025-07-23 22:26:37,607 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:37,607 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.bin
2025-07-23 22:26:37,657 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:37,657 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:26:39,744 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg for user test_download_user
2025-07-23 22:26:39,746 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:26:39,746 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_222635_test_config.bin
2025-07-23 22:26:39,804 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:26:39,805 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:28:31,405 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-23 22:28:31,406 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222831_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:28:31,408 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:28:31,408 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222831_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_222831_Wrong_PQcfg.bin
2025-07-23 22:28:31,556 - main - INFO - External tool found 5 validation warnings
2025-07-23 22:28:31,556 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-23 22:28:31,558 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-23 22:33:31,400 - main - INFO - Processing upload: test_config.cfg (235.0 B) for user test_download_user
2025-07-23 22:33:31,401 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg for user test_download_user
2025-07-23 22:33:31,403 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:31,403 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.bin
2025-07-23 22:33:31,464 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:31,465 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:31,466 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:33,508 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg for user test_download_user
2025-07-23 22:33:33,509 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:33,510 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.bin
2025-07-23 22:33:33,551 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:33,551 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:35,675 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg for user test_download_user
2025-07-23 22:33:35,702 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:35,713 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223331_test_config.bin
2025-07-23 22:33:35,789 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:35,790 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:54,844 - main - INFO - Processing upload: test_config.cfg (235.0 B) for user test_download_user
2025-07-23 22:33:54,845 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg for user test_download_user
2025-07-23 22:33:54,846 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:54,847 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.bin
2025-07-23 22:33:54,890 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:54,891 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:54,893 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:56,913 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg for user test_download_user
2025-07-23 22:33:56,914 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:56,914 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.bin
2025-07-23 22:33:56,961 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:56,962 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:33:59,039 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg for user test_download_user
2025-07-23 22:33:59,039 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:33:59,040 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_download_user\20250723_223354_test_config.bin
2025-07-23 22:33:59,083 - main - INFO - External tool found 8 validation warnings
2025-07-23 22:33:59,083 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).
2025-07-23 22:34:45,544 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-23 22:34:45,604 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:34:45,675 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:34:45,758 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.bin
2025-07-23 22:34:46,574 - main - INFO - External tool found 5 validation warnings
2025-07-23 22:34:46,604 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-23 22:34:46,662 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-23 22:34:54,672 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:34:54,759 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:34:54,849 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_223445_Wrong_PQcfg.bin
2025-07-23 22:34:55,384 - main - INFO - External tool found 5 validation warnings
2025-07-23 22:34:55,423 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-23 22:57:33,037 - main - INFO - Processing upload: test_config.txt (297.0 B) for user test_dual_format_user
2025-07-23 22:57:33,039 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225733_test_config.txt for user test_dual_format_user
2025-07-23 22:57:33,045 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:57:33,045 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225733_test_config.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225733_test_config.bin
2025-07-23 22:57:33,107 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 22:57:33,108 - main - INFO - External tool found 4 validation warnings
2025-07-23 22:57:33,108 - main - INFO - Analysis complete. Found 3 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:57:33,110 - main - INFO - File processing complete: Analysis complete. Found 3 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:57:35,152 - main - INFO - Processing upload: test_config.cfg (329.0 B) for user test_dual_format_user
2025-07-23 22:57:35,155 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg for user test_dual_format_user
2025-07-23 22:57:35,159 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:57:35,159 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.bin
2025-07-23 22:57:35,251 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 22:57:35,253 - main - INFO - External tool found 4 validation warnings
2025-07-23 22:57:35,254 - main - INFO - Analysis complete. Found 2 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:57:35,258 - main - INFO - File processing complete: Analysis complete. Found 2 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:57:39,350 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg for user test_dual_format_user
2025-07-23 22:57:39,351 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:57:39,351 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.bin
2025-07-23 22:57:39,395 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 22:57:39,395 - main - INFO - External tool found 4 validation warnings
2025-07-23 22:57:39,395 - main - INFO - Analysis complete. Found 2 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:57:41,466 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg for user test_dual_format_user
2025-07-23 22:57:41,468 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:57:41,469 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_dual_format_user\20250723_225735_test_config.bin
2025-07-23 22:57:41,507 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 22:57:41,508 - main - INFO - External tool found 4 validation warnings
2025-07-23 22:57:41,508 - main - INFO - Analysis complete. Found 2 undefined parameter(s) and 4 validation warning(s).
2025-07-23 22:58:27,415 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-23 22:58:27,417 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:58:27,418 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:58:27,419 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.bin
2025-07-23 22:58:27,478 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-23 22:58:27,479 - main - INFO - External tool found 2 validation warnings
2025-07-23 22:58:27,479 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-23 22:58:27,481 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-23 22:58:38,778 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.cfg for user Ethan
2025-07-23 22:58:38,780 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 22:58:38,780 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250723_225827_Wrong_PQcfg.bin
2025-07-23 22:58:38,829 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-23 22:58:38,830 - main - INFO - External tool found 2 validation warnings
2025-07-23 22:58:38,830 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-23 23:19:13,338 - main - INFO - Processing upload: deployment_test.txt (382.0 B) for user deployment_test_user_1753337949
2025-07-23 23:19:13,339 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231913_deployment_test.txt for user deployment_test_user_1753337949
2025-07-23 23:19:13,341 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 23:19:13,342 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231913_deployment_test.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231913_deployment_test.bin
2025-07-23 23:19:13,393 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 23:19:13,393 - main - INFO - External tool found 4 validation warnings
2025-07-23 23:19:13,394 - main - INFO - Analysis complete. Found 4 undefined parameter(s) and 4 validation warning(s).
2025-07-23 23:19:13,397 - main - INFO - File processing complete: Analysis complete. Found 4 undefined parameter(s) and 4 validation warning(s).
2025-07-23 23:19:15,434 - main - INFO - Processing upload: deployment_test.cfg (483.0 B) for user deployment_test_user_1753337949
2025-07-23 23:19:15,435 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg for user deployment_test_user_1753337949
2025-07-23 23:19:15,436 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 23:19:15,436 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.bin
2025-07-23 23:19:15,477 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 23:19:15,477 - main - INFO - External tool found 4 validation warnings
2025-07-23 23:19:15,477 - main - INFO - Analysis complete. Found 6 undefined parameter(s) and 4 validation warning(s).
2025-07-23 23:19:15,479 - main - INFO - File processing complete: Analysis complete. Found 6 undefined parameter(s) and 4 validation warning(s).
2025-07-23 23:19:17,512 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg for user deployment_test_user_1753337949
2025-07-23 23:19:17,513 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 23:19:17,513 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.bin
2025-07-23 23:19:17,552 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 23:19:17,552 - main - INFO - External tool found 4 validation warnings
2025-07-23 23:19:17,552 - main - INFO - Analysis complete. Found 6 undefined parameter(s) and 4 validation warning(s).
2025-07-23 23:19:19,618 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg for user deployment_test_user_1753337949
2025-07-23 23:19:19,619 - main - INFO - Running external Dolby Vision validation tool
2025-07-23 23:19:19,620 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\deployment_test_user_1753337949\20250723_231915_deployment_test.bin
2025-07-23 23:19:19,660 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-23 23:19:19,661 - main - INFO - External tool found 4 validation warnings
2025-07-23 23:19:19,661 - main - INFO - Analysis complete. Found 6 undefined parameter(s) and 4 validation warning(s).
2025-07-24 13:34:49,144 - main - INFO - Processing upload: L17-IC0-10-issue_PQ.txt (6.2 KB) for user Ethan
2025-07-24 13:34:49,147 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133449_L17-IC0-10-issue_PQ.txt for user Ethan
2025-07-24 13:34:49,150 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:34:49,151 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133449_L17-IC0-10-issue_PQ.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133449_L17-IC0-10-issue_PQ.bin
2025-07-24 13:34:49,197 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-07-24 13:34:49,197 - main - INFO - External tool found 7 validation warnings
2025-07-24 13:34:49,198 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-07-24 13:34:49,201 - main - INFO - File processing complete: Analysis complete. Found 7 validation warning(s).
2025-07-24 13:37:35,477 - main - INFO - Processing upload: L17-IC0-10-issue_PQ.txt (6.2 KB) for user Ethan
2025-07-24 13:37:35,484 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133735_L17-IC0-10-issue_PQ.txt for user Ethan
2025-07-24 13:37:35,487 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:37:35,487 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133735_L17-IC0-10-issue_PQ.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_133735_L17-IC0-10-issue_PQ.bin
2025-07-24 13:37:35,529 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-07-24 13:37:35,529 - main - INFO - External tool found 7 validation warnings
2025-07-24 13:37:35,529 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-07-24 13:37:35,532 - main - INFO - File processing complete: Analysis complete. Found 7 validation warning(s).
2025-07-24 13:42:42,874 - main - INFO - Processing upload: DOLBY_FACTORY-beta6.cfg (6.2 KB) for user kennywu
2025-07-24 13:42:42,875 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_134242_DOLBY_FACTORY-beta6.cfg for user kennywu
2025-07-24 13:42:42,878 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:42:42,878 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_134242_DOLBY_FACTORY-beta6.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_134242_DOLBY_FACTORY-beta6.bin
2025-07-24 13:42:42,919 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-24 13:42:42,920 - main - INFO - External tool found 5 validation warnings
2025-07-24 13:42:42,920 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-24 13:42:42,925 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-24 13:43:29,678 - main - INFO - Processing upload: L17-IC0-10-issue_PQ.txt (6.2 KB) for user Ethan
2025-07-24 13:43:29,679 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_134329_L17-IC0-10-issue_PQ.txt for user Ethan
2025-07-24 13:43:29,683 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:43:29,684 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_134329_L17-IC0-10-issue_PQ.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_134329_L17-IC0-10-issue_PQ.bin
2025-07-24 13:43:29,725 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-07-24 13:43:29,725 - main - INFO - External tool found 7 validation warnings
2025-07-24 13:43:29,726 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-07-24 13:43:29,729 - main - INFO - File processing complete: Analysis complete. Found 7 validation warning(s).
2025-07-24 13:44:29,512 - main - INFO - Processing upload: 2024_12_17_15_50_01_PQ.cfg (4.8 KB) for user Aman
2025-07-24 13:44:29,517 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Aman\20250724_134429_2024_12_17_15_50_01_PQ.cfg for user Aman
2025-07-24 13:44:29,519 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:44:29,519 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Aman\20250724_134429_2024_12_17_15_50_01_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Aman\20250724_134429_2024_12_17_15_50_01_PQ.bin
2025-07-24 13:44:29,554 - main - INFO - External tool found 3 unique validation warnings (after deduplication)
2025-07-24 13:44:29,554 - main - INFO - External tool found 3 validation warnings
2025-07-24 13:44:29,554 - main - INFO - Analysis complete. Found 3 validation warning(s).
2025-07-24 13:44:29,557 - main - INFO - File processing complete: Analysis complete. Found 3 validation warning(s).
2025-07-24 13:57:30,902 - main - INFO - Processing upload: cfg_0.05_364_PQ_2.2_YUV_NARROW_v2_L17_IC.txt (7.7 KB) for user EZ
2025-07-24 13:57:30,902 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135730_cfg_0.05_364_PQ_2.2_YUV_NARROW_v2_L17_IC.txt for user EZ
2025-07-24 13:57:30,907 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:57:30,908 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135730_cfg_0.05_364_PQ_2.2_YUV_NARROW_v2_L17_IC.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135730_cfg_0.05_364_PQ_2.2_YUV_NARROW_v2_L17_IC.bin
2025-07-24 13:57:30,950 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-07-24 13:57:30,950 - main - INFO - External tool found 1 validation warnings
2025-07-24 13:57:30,951 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-24 13:57:30,953 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-24 13:58:00,028 - main - INFO - Processing upload: blending.cfg (6.4 KB) for user EZ
2025-07-24 13:58:00,029 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135800_blending.cfg for user EZ
2025-07-24 13:58:00,032 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:58:00,033 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135800_blending.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_135800_blending.bin
2025-07-24 13:58:00,071 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-07-24 13:58:00,071 - main - INFO - External tool found 6 validation warnings
2025-07-24 13:58:00,071 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-07-24 13:58:00,074 - main - INFO - File processing complete: Analysis complete. Found 6 validation warning(s).
2025-07-24 13:59:19,054 - main - INFO - Processing upload: DOLBY_FACTORY-beta6.cfg (6.2 KB) for user kennywu
2025-07-24 13:59:19,055 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_135919_DOLBY_FACTORY-beta6.cfg for user kennywu
2025-07-24 13:59:19,058 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 13:59:19,059 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_135919_DOLBY_FACTORY-beta6.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\kennywu\20250724_135919_DOLBY_FACTORY-beta6.bin
2025-07-24 13:59:19,109 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-07-24 13:59:19,109 - main - INFO - External tool found 1 validation warnings
2025-07-24 13:59:19,109 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-24 13:59:19,113 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-24 14:01:55,520 - main - INFO - Processing upload: L17-IC0-10-issue_PQ-1.cfg (6.2 KB) for user Ethan
2025-07-24 14:01:55,521 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg for user Ethan
2025-07-24 14:01:55,524 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:01:55,525 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.bin
2025-07-24 14:01:55,563 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-07-24 14:01:55,564 - main - INFO - External tool found 6 validation warnings
2025-07-24 14:01:55,564 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-07-24 14:01:55,567 - main - INFO - File processing complete: Analysis complete. Found 6 validation warning(s).
2025-07-24 14:06:31,887 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg for user Ethan
2025-07-24 14:06:31,890 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:06:31,891 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.bin
2025-07-24 14:06:31,937 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-07-24 14:06:31,937 - main - INFO - External tool found 6 validation warnings
2025-07-24 14:06:31,938 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-07-24 14:07:50,778 - main - INFO - Processing upload: disable-l15l17IC.cfg (6.3 KB) for user EZ
2025-07-24 14:07:50,782 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_140750_disable-l15l17IC.cfg for user EZ
2025-07-24 14:07:50,782 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:07:50,782 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_140750_disable-l15l17IC.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_140750_disable-l15l17IC.bin
2025-07-24 14:07:50,825 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-07-24 14:07:50,825 - main - INFO - External tool found 6 validation warnings
2025-07-24 14:07:50,825 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-07-24 14:07:50,834 - main - INFO - File processing complete: Analysis complete. Found 6 validation warning(s).
2025-07-24 14:08:37,244 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg for user Ethan
2025-07-24 14:08:37,244 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:08:37,244 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_140155_L17-IC0-10-issue_PQ-1.bin
2025-07-24 14:08:37,285 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-07-24 14:08:37,285 - main - INFO - External tool found 6 validation warnings
2025-07-24 14:08:37,285 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-07-24 14:10:16,717 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Ethan
2025-07-24 14:10:16,722 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.cfg for user Ethan
2025-07-24 14:10:16,727 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:10:16,727 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:10:16,770 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-07-24 14:10:16,770 - main - INFO - External tool found 7 validation warnings
2025-07-24 14:10:16,770 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-07-24 14:10:16,770 - main - INFO - File processing complete: Analysis complete. Found 7 validation warning(s).
2025-07-24 14:11:42,280 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.cfg for user Ethan
2025-07-24 14:11:42,280 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:11:42,290 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141016_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:11:42,323 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-07-24 14:11:42,323 - main - INFO - External tool found 7 validation warnings
2025-07-24 14:11:42,323 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-07-24 14:13:57,156 - main - INFO - Processing upload: DVSDK_7353_PQ.cfg (6.2 KB) for user Ethan
2025-07-24 14:13:57,157 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.cfg for user Ethan
2025-07-24 14:13:57,159 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:13:57,160 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.bin
2025-07-24 14:13:57,200 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-24 14:13:57,200 - main - INFO - External tool found 5 validation warnings
2025-07-24 14:13:57,200 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-24 14:13:57,200 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-24 14:15:10,611 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.cfg for user Ethan
2025-07-24 14:15:10,611 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:15:10,611 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250724_141357_DVSDK_7353_PQ.bin
2025-07-24 14:15:10,659 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-24 14:15:10,660 - main - INFO - External tool found 5 validation warnings
2025-07-24 14:15:10,660 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-24 14:27:26,879 - main - INFO - Processing upload: scenario_reproduce kenny.txt (2.5 KB) for user EZ
2025-07-24 14:27:26,881 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_142726_scenario_reproduce kenny.txt for user EZ
2025-07-24 14:27:26,883 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:27:26,884 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_142726_scenario_reproduce kenny.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\EZ\20250724_142726_scenario_reproduce kenny.bin
2025-07-24 14:27:26,921 - main - INFO - External tool found 4 unique validation warnings (after deduplication)
2025-07-24 14:27:26,922 - main - INFO - External tool found 4 validation warnings
2025-07-24 14:27:26,922 - main - INFO - Analysis complete. Found 4 validation warning(s).
2025-07-24 14:27:26,924 - main - INFO - File processing complete: Analysis complete. Found 4 validation warning(s).
2025-07-24 14:32:09,256 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:32:09,262 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143209_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:32:09,268 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:32:09,268 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143209_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143209_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:32:09,311 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-24 14:32:09,311 - main - INFO - External tool found 5 validation warnings
2025-07-24 14:32:09,311 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-24 14:32:09,315 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-24 14:34:56,153 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:34:56,154 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143456_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:34:56,158 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:34:56,159 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143456_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143456_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:34:56,197 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-24 14:34:56,197 - main - INFO - External tool found 5 validation warnings
2025-07-24 14:34:56,198 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-24 14:34:56,201 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-24 14:36:26,500 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:36:26,505 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143626_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:36:26,510 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:36:26,510 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143626_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143626_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:36:26,561 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-07-24 14:36:26,562 - main - INFO - External tool found 1 validation warnings
2025-07-24 14:36:26,562 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-24 14:36:26,565 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-24 14:37:42,071 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:37:42,073 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143742_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:37:42,077 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:37:42,077 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143742_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_143742_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:37:42,120 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-24 14:37:42,120 - main - INFO - External tool found 0 validation warnings
2025-07-24 14:37:42,120 - main - INFO - Analysis complete. No issues found.
2025-07-24 14:37:42,124 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-24 14:53:39,791 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:53:39,796 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145339_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:53:39,802 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:53:39,803 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145339_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145339_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:53:39,846 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-24 14:53:39,846 - main - INFO - External tool found 0 validation warnings
2025-07-24 14:53:39,846 - main - INFO - Analysis complete. No issues found.
2025-07-24 14:53:39,850 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-24 14:53:54,383 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ.cfg (6.2 KB) for user Pranjali
2025-07-24 14:53:54,384 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145354_2025_07_21_17_22_34_PQ.cfg for user Pranjali
2025-07-24 14:53:54,387 - main - INFO - Running external Dolby Vision validation tool
2025-07-24 14:53:54,388 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145354_2025_07_21_17_22_34_PQ.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Pranjali\20250724_145354_2025_07_21_17_22_34_PQ.bin
2025-07-24 14:53:54,424 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-24 14:53:54,424 - main - INFO - External tool found 0 validation warnings
2025-07-24 14:53:54,424 - main - INFO - Analysis complete. No issues found.
2025-07-24 14:53:54,426 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-24 19:06:31,881 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:06:31,886 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:06:31,899 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:06:31,899 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:06:31,900 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:06:31,900 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:06:31,965 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:16:16,235 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:16:16,236 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:16:16,251 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:16:16,251 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:16:16,251 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:16:16,251 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:16:16,346 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:16:16,387 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:16:16,387 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:16:16,387 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 19:18:03,025 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:18:03,025 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:18:03,040 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:18:03,041 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:18:03,041 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:18:03,042 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:18:03,150 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:18:03,207 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:18:03,207 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:18:03,207 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 19:19:43,864 - config_validator_framework - INFO - Created session e3c8ef38-b2f0-442e-9bc0-3d5464890e50 for user test_framework_user
2025-07-24 19:20:28,059 - config_validator_framework - INFO - Created session 1b7c7371-8c1d-4e27-8574-1fa4cda19548 for user test_framework_user
2025-07-24 19:21:14,209 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:21:14,209 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:21:14,219 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:21:14,219 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:21:14,219 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:21:14,219 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:21:14,291 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:21:14,323 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:21:14,323 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:21:14,323 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 19:22:11,989 - config_validator_framework - INFO - Created session f3319218-c6ea-4f0c-926e-ce345af510e5 for user test_framework_user
2025-07-24 19:25:21,832 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:25:21,833 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:25:21,845 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:25:21,845 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:25:21,845 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:25:21,846 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:25:21,917 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:25:21,945 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:25:21,946 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:25:21,947 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 19:25:45,193 - config_validator_framework - INFO - Created session be3d514c-4ad5-4207-b7eb-89a870131142 for user test_framework_user
2025-07-24 19:26:31,017 - config_validator_framework - INFO - Created session 4f41926e-bff1-4147-a714-249c8b2ed417 for user test_upload_user
2025-07-24 19:26:57,071 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:26:57,071 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:26:57,086 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:26:57,087 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:26:57,087 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:26:57,088 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:26:57,158 - config_validator_framework - INFO - Starting server on 0.0.0.0:8000
2025-07-24 19:26:57,190 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:26:57,191 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:26:57,191 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 19:29:10,730 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:29:10,731 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:29:10,742 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:29:10,743 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:29:10,743 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:29:10,744 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:29:27,495 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 19:29:27,495 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 19:29:27,508 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 19:29:27,508 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 19:29:27,509 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 19:29:27,509 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:29:27,518 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 19:29:27,518 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 19:29:27,519 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 20:56:37,123 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 20:56:37,125 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 20:56:37,133 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 20:56:37,134 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 20:56:37,134 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 20:56:37,134 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 20:56:37,296 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 20:56:37,296 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 20:56:37,297 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 21:00:44,782 - config_validator_framework - INFO - Created session 7009719b-ea7e-41c3-af3d-763067f64cf0 for user test_upload_user
2025-07-24 21:08:27,193 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 21:08:27,193 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 21:08:27,207 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 21:08:27,207 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 21:08:27,208 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 21:08:27,209 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:08:27,316 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 21:08:27,316 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:08:27,317 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 21:08:53,057 - config_validator_framework - INFO - Created session 00f222a1-5835-450a-9316-cee126353fd7 for user test_upload_user
2025-07-24 21:08:53,073 - config_validator_framework - ERROR - Upload page error: 'author' is undefined
2025-07-24 21:20:13,490 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 21:20:13,491 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 21:20:13,498 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 21:20:13,499 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 21:20:13,500 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 21:20:13,500 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:20:13,607 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 21:20:13,612 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:20:13,613 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 21:20:37,004 - config_validator_framework - INFO - Created session 2e67f004-8426-4c8b-a6f9-6a0ba0acb0b5 for user test_upload_user
2025-07-24 21:20:37,020 - config_validator_framework - ERROR - Upload page error: 'author' is undefined
2025-07-24 21:21:43,523 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 21:21:43,524 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 21:21:43,537 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 21:21:43,538 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 21:21:43,538 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 21:21:43,539 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:21:43,650 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 21:21:43,651 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 21:21:43,651 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 21:22:17,935 - config_validator_framework - INFO - Created session 4b018881-5a9b-4f7b-a357-6f34959ea7b7 for user test_upload_user
2025-07-24 21:22:33,616 - config_validator_framework - INFO - Created session 3a07e29f-e5e0-47f2-ae74-d18fb3ce7d82 for user test_framework_user
2025-07-24 22:35:08,243 - config_validator_framework - INFO - Created session d2bfe2ab-5a4c-42e2-8a0f-0766e984a3c6 for user test_upload_user
2025-07-24 22:35:08,271 - config_validator_framework - INFO - Processing file test_config.cfg for user test_upload_user
2025-07-24 22:35:08,272 - config_validator_framework.core.application - INFO - Processing file test_config.cfg for user test_upload_user
2025-07-24 22:35:08,276 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\test_upload_user\test_config.cfg -b uploads\test_upload_user\test_config.bin
2025-07-24 22:35:08,337 - config_validator_framework.core.application - INFO - External tool found 1 validation results
2025-07-24 22:35:08,338 - config_validator_framework.core.application - INFO - External tool validation completed for test_config.cfg
2025-07-24 22:35:08,339 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. No issues found.
2025-07-24 22:35:45,899 - config_validator_framework - INFO - Created session f9197753-bbd1-4ba0-968f-bd1497d02e61 for user test_issues_user
2025-07-24 22:35:45,908 - config_validator_framework - INFO - Processing file test_with_issues.cfg for user test_issues_user
2025-07-24 22:35:45,909 - config_validator_framework.core.application - INFO - Processing file test_with_issues.cfg for user test_issues_user
2025-07-24 22:35:45,912 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\test_issues_user\test_with_issues.cfg -b uploads\test_issues_user\test_with_issues.bin
2025-07-24 22:35:45,967 - config_validator_framework.core.application - INFO - External tool found 1 validation results
2025-07-24 22:35:45,967 - config_validator_framework.core.application - INFO - External tool validation completed for test_with_issues.cfg
2025-07-24 22:35:45,968 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. No issues found.
2025-07-24 23:00:47,338 - config_validator_framework - INFO - Created session 959a5452-5dd9-489f-a782-b8488e40284b for user test_proper_user
2025-07-24 23:00:47,346 - config_validator_framework - INFO - Processing file test_proper_config.cfg for user test_proper_user
2025-07-24 23:00:47,347 - config_validator_framework.core.application - INFO - Processing file test_proper_config.cfg for user test_proper_user
2025-07-24 23:00:47,351 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\test_proper_user\test_proper_config.cfg -b uploads\test_proper_user\test_proper_config.bin
2025-07-24 23:00:47,397 - config_validator_framework.core.application - INFO - External tool found 1 validation results
2025-07-24 23:00:47,398 - config_validator_framework.core.application - INFO - External tool validation completed for test_proper_config.cfg
2025-07-24 23:00:47,399 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. Found 9 undefined parameter(s) and 0 validation warning(s).
2025-07-24 23:04:31,072 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:04:31,072 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:04:31,087 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:04:31,087 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:04:31,088 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:04:31,088 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:04:31,200 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:04:31,201 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:04:31,201 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 23:05:21,914 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:05:21,914 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:05:21,926 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:05:21,927 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:05:21,927 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:05:21,927 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:05:22,060 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:05:22,060 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:05:22,060 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 23:05:24,684 - config_validator_framework - INFO - Created session 7dccab90-260c-4eac-9c62-751c62b59b2e for user test_complete_user
2025-07-24 23:05:24,700 - config_validator_framework - INFO - Processing file test_config.cfg for user test_complete_user
2025-07-24 23:05:24,701 - config_validator_framework.core.application - INFO - Processing file test_config.cfg for user test_complete_user
2025-07-24 23:05:24,702 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\test_complete_user\test_config.cfg -b uploads\test_complete_user\test_config.bin
2025-07-24 23:05:24,750 - config_validator_framework.core.application - INFO - External tool found 1 validation results
2025-07-24 23:05:24,750 - config_validator_framework.core.application - INFO - External tool validation completed for test_config.cfg
2025-07-24 23:05:24,751 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. Found 5 undefined parameter(s) and 0 validation warning(s).
2025-07-24 23:06:00,171 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:06:00,173 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:06:00,188 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:06:00,189 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:06:00,190 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:06:00,190 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:06:00,322 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:06:00,322 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:06:00,323 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 23:06:02,923 - config_validator_framework - INFO - Created session 2681b835-afdd-4ff1-a9a9-21b26e3a28fd for user test_complete_user
2025-07-24 23:06:02,947 - config_validator_framework - INFO - Processing file test_config.cfg for user test_complete_user
2025-07-24 23:06:02,947 - config_validator_framework.core.application - INFO - Processing file test_config.cfg for user test_complete_user
2025-07-24 23:06:02,950 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\test_complete_user\test_config.cfg -b uploads\test_complete_user\test_config.bin
2025-07-24 23:06:03,007 - config_validator_framework.core.application - INFO - External tool found 1 validation results
2025-07-24 23:06:03,008 - config_validator_framework.core.application - INFO - External tool validation completed for test_config.cfg
2025-07-24 23:06:03,008 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. Found 5 undefined parameter(s) and 0 validation warning(s).
2025-07-24 23:06:16,313 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:06:16,313 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:06:16,322 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:06:16,322 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:06:16,323 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:06:16,323 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:06:16,473 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:06:16,474 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:06:16,474 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 23:08:39,026 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:08:39,027 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:08:39,039 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:08:39,040 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:08:39,040 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:08:39,041 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:08:56,970 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:08:56,971 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:08:56,985 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:08:56,985 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:08:56,987 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:08:56,988 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:08:56,994 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:08:56,995 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:08:56,995 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-24 23:10:13,201 - config_validator_framework - INFO - Created session 1509cd74-551c-481d-85d6-1eb8451fa765 for user test_browser_user
2025-07-24 23:10:40,727 - config_validator_framework - INFO - Created session e4a6be1c-4aa3-4ecc-9798-8c6e7b5b4370 for user Ethan
2025-07-24 23:11:09,167 - config_validator_framework - INFO - Processing file Wrong_PQcfg.cfg for user Ethan
2025-07-24 23:11:09,169 - config_validator_framework.core.application - INFO - Processing file Wrong_PQcfg.cfg for user Ethan
2025-07-24 23:11:09,173 - config_validator_framework.core.application - INFO - Running external validation: bin/dtv_config_lib_test.exe -t uploads\Ethan\Wrong_PQcfg.cfg -b uploads\Ethan\Wrong_PQcfg.bin
2025-07-24 23:11:09,174 - config_validator_framework.core.application - ERROR - External tool validation failed: External tool execution failed: 
2025-07-24 23:11:09,175 - config_validator_framework.core.application - INFO - File processing complete: Analysis complete. Found 0 undefined parameter(s), 0 warning(s), and 1 error(s).
2025-07-24 23:11:28,817 - config_validator_framework - INFO - Created session 85c8cdc8-3296-4fa0-affa-c685948b5f09 for user browser_test_user
2025-07-24 23:12:27,729 - config_validator_framework - INFO - Logging initialized at level INFO
2025-07-24 23:12:27,730 - config_validator_framework - INFO - Log file: logs\app.log
2025-07-24 23:12:27,749 - config_validator_framework.core.application - INFO - Initialized PQ Config Validator v1.0.0
2025-07-24 23:12:27,749 - config_validator_framework.core.application - INFO - External tool: Dolby Vision Config Validator
2025-07-24 23:12:27,750 - config_validator_framework.core.application - INFO - Parameter detector: Pattern-based Parameter Detector
2025-07-24 23:12:27,750 - config_validator_framework.core.application - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:12:27,770 - config_validator_framework - INFO - Starting PQ Config Validator v1.0.0
2025-07-24 23:12:27,772 - config_validator_framework - INFO - Supported formats: ['.cfg', '.txt']
2025-07-24 23:12:27,773 - config_validator_framework - INFO - Max file size: 10,485,760 bytes
2025-07-26 18:34:53,595 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 18:34:53,595 - __main__ - INFO - Author: Ethan Li
2025-07-26 18:34:53,596 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 18:35:59,898 - __main__ - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-26 18:35:59,900 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_183559_Wrong_PQcfg.cfg for user Ethan
2025-07-26 18:35:59,904 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-26 18:35:59,906 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_183559_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_183559_Wrong_PQcfg.bin
2025-07-26 18:35:59,958 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-26 18:35:59,958 - __main__ - INFO - External tool found 2 validation warnings
2025-07-26 18:35:59,959 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-26 18:35:59,964 - __main__ - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-26 18:53:21,163 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,693 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,694 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 18:54:09,695 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 18:54:09,695 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:09,695 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 18:54:09,695 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 18:54:09,695 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,254 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,255 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,255 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,255 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,255 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,256 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,256 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,256 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 18:54:53,256 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 18:54:53,256 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:54:53,256 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 18:54:53,256 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 18:54:53,256 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,202 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,202 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,202 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,202 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,202 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,203 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,203 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,203 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 18:55:05,203 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 18:55:05,203 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 18:55:05,203 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 18:55:05,203 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 18:55:05,203 - web_app_registry - INFO - Loaded 3 web applications
2025-07-26 19:12:19,244 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 19:12:19,245 - __main__ - INFO - Author: Ethan Li
2025-07-26 19:12:19,245 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 19:17:54,604 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,604 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,604 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 19:17:54,612 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 19:17:54,612 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:17:54,612 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,276 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,276 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,276 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,276 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,276 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,277 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,277 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,277 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 19:19:44,277 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 19:19:44,277 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,277 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 19:19:44,277 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 19:19:44,278 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 19:19:44,278 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 21:02:02,595 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 21:02:02,600 - __main__ - INFO - Author: Ethan Li
2025-07-26 21:02:02,600 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 21:10:59,683 - main - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 21:10:59,690 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211059_L17-IC-filter_1.txt
2025-07-26 21:10:59,690 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211059_L17-IC-filter_1.txt for user Ethan
2025-07-26 21:10:59,819 - main - INFO - Running external Dolby Vision validation tool
2025-07-26 21:10:59,820 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211059_L17-IC-filter_1.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211059_L17-IC-filter_1.bin
2025-07-26 21:11:03,614 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 21:11:03,616 - main - WARNING - External validation tool failed or not available
2025-07-26 21:11:03,616 - main - INFO - Analysis complete. No issues found.
2025-07-26 21:11:03,623 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 21:18:32,160 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-26 21:18:32,174 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211832_L17-IC-filter_0.5.txt
2025-07-26 21:18:32,174 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211832_L17-IC-filter_0.5.txt for user Ethan
2025-07-26 21:18:32,351 - main - INFO - Running external Dolby Vision validation tool
2025-07-26 21:18:32,352 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211832_L17-IC-filter_0.5.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_211832_L17-IC-filter_0.5.bin
2025-07-26 21:18:35,752 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 21:18:35,752 - main - WARNING - External validation tool failed or not available
2025-07-26 21:18:35,753 - main - INFO - Analysis complete. No issues found.
2025-07-26 21:18:35,760 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 21:53:20,386 - log_processors - WARNING - File is empty: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-0\test_empty_file0\empty.txt
2025-07-26 21:53:20,397 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-0\test_nonexistent_file0\nonexistent.txt
2025-07-26 21:53:20,592 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-0\test_detect_tc_flash_log0\test_log.txt
2025-07-26 21:53:20,601 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-0\test_detect_pq_config0\test_config.txt
2025-07-26 21:58:48,719 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 21:58:48,719 - __main__ - INFO - Author: Ethan Li
2025-07-26 21:58:48,719 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 21:59:57,964 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 21:59:57,964 - __main__ - INFO - Author: Ethan Li
2025-07-26 21:59:57,965 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 22:02:10,262 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 22:02:10,263 - __main__ - INFO - Author: Ethan Li
2025-07-26 22:02:10,263 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 22:10:03,151 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 22:10:03,151 - __main__ - INFO - Author: Ethan Li
2025-07-26 22:10:03,151 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 22:14:07,451 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-1\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:14:07,462 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-1\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-1\test_execute_grid_comparison_p0\output
2025-07-26 22:14:07,465 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753593247_all_parameters_comparison_grid.png
2025-07-26 22:14:07,476 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:14:07,761 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-1\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:15:27,399 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-2\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:15:27,409 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-2\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-2\test_execute_grid_comparison_p0\output
2025-07-26 22:15:27,413 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753593327_all_parameters_comparison_grid.png
2025-07-26 22:15:27,421 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:15:27,607 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-2\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:15:27,614 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-2\test_processor_recommendation0\test_log.txt
2025-07-26 22:16:13,057 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:16:13,060 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-3\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:16:13,060 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-3\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:16:40,807 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:16:40,817 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_execute_grid_comparison_p0\output
2025-07-26 22:16:40,821 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753593400_all_parameters_comparison_grid.png
2025-07-26 22:16:40,833 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:16:40,835 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:16:40,836 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:16:40,866 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:16:40,875 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-4\test_processor_recommendation0\test_log.txt
2025-07-26 22:18:27,023 - main - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 22:18:27,030 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_221827_L17-IC-filter_1.txt
2025-07-26 22:18:27,030 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_221827_L17-IC-filter_1.txt for user Ethan
2025-07-26 22:18:27,176 - main - INFO - Running external Dolby Vision validation tool
2025-07-26 22:18:27,176 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_221827_L17-IC-filter_1.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_221827_L17-IC-filter_1.bin
2025-07-26 22:18:31,251 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 22:18:31,251 - main - WARNING - External validation tool failed or not available
2025-07-26 22:18:31,270 - main - INFO - Analysis complete. No issues found.
2025-07-26 22:18:31,278 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 22:20:40,213 - log_processors - WARNING - File is empty: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_empty_file0\empty.txt
2025-07-26 22:20:40,221 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:20:40,402 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_detect_tc_flash_log0\test_log.txt
2025-07-26 22:20:40,412 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_detect_pq_config0\test_config.txt
2025-07-26 22:20:40,494 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,498 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,504 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,511 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,519 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,523 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,531 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,531 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:20:40,531 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:20:40,537 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,537 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:20:40,537 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:20:40,543 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,548 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:20:40,576 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_nonexistent_file1\nonexistent.txt
2025-07-26 22:20:40,595 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_execute_grid_comparison_p0\output
2025-07-26 22:20:40,599 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753593640_all_parameters_comparison_grid.png
2025-07-26 22:20:40,612 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:20:40,615 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:20:40,615 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:20:40,647 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:20:40,655 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-5\test_processor_recommendation0\test_log.txt
2025-07-26 22:22:09,497 - log_processors - WARNING - File is empty: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_empty_file0\empty.txt
2025-07-26 22:22:09,501 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:22:09,704 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_detect_tc_flash_log0\test_log.txt
2025-07-26 22:22:09,715 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_detect_pq_config0\test_config.txt
2025-07-26 22:22:09,784 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,787 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,789 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,793 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,797 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,800 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,803 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,804 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:22:09,804 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:22:09,808 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,809 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:22:09,809 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:22:09,814 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,818 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:22:09,841 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_nonexistent_file1\nonexistent.txt
2025-07-26 22:22:09,855 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_execute_grid_comparison_p0\output
2025-07-26 22:22:09,858 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753593729_all_parameters_comparison_grid.png
2025-07-26 22:22:09,867 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:22:09,869 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:22:09,870 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:22:09,894 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:22:09,901 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-6\test_processor_recommendation0\test_log.txt
2025-07-26 22:26:52,133 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:02,813 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,070 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,075 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,075 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,075 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,086 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,086 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,086 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,086 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:27:49,094 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:27:49,097 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,097 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:27:49,097 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:27:49,103 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:27:49,108 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,083 - log_processors - WARNING - File is empty: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_empty_file0\empty.txt
2025-07-26 22:29:24,089 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:29:24,271 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_detect_tc_flash_log0\test_log.txt
2025-07-26 22:29:24,281 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_detect_pq_config0\test_config.txt
2025-07-26 22:29:24,342 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,346 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,350 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,356 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,365 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,375 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,377 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,378 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:29:24,379 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:29:24,383 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,383 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:29:24,383 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:29:24,389 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,394 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:24,426 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_nonexistent_file1\nonexistent.txt
2025-07-26 22:29:24,440 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_execute_grid_comparison_p0\output
2025-07-26 22:29:24,444 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753594164_all_parameters_comparison_grid.png
2025-07-26 22:29:24,463 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:29:24,467 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:29:24,467 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:29:24,493 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:29:24,501 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-7\test_processor_recommendation0\test_log.txt
2025-07-26 22:29:42,853 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,856 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,859 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,861 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,866 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,869 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,873 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,873 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:29:42,873 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:29:42,876 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,877 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:29:42,877 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:29:42,880 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,884 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:29:42,949 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:29:42,959 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_execute_grid_comparison_p0\output
2025-07-26 22:29:42,962 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753594182_all_parameters_comparison_grid.png
2025-07-26 22:29:42,972 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:29:42,973 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:29:42,974 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:29:42,996 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:29:43,001 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-8\test_processor_recommendation0\test_log.txt
2025-07-26 22:29:54,900 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 22:29:54,900 - __main__ - INFO - Author: Ethan Li
2025-07-26 22:29:54,901 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 22:31:16,669 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,671 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,673 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,675 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,678 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,680 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,682 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,683 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:31:16,683 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:31:16,685 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,686 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:31:16,686 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:31:16,688 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,690 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:31:16,742 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:31:16,753 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_execute_grid_comparison_p0\output
2025-07-26 22:31:16,756 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753594276_all_parameters_comparison_grid.png
2025-07-26 22:31:16,763 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:31:16,765 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:31:16,765 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:31:16,785 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:31:16,792 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-9\test_processor_recommendation0\test_log.txt
2025-07-26 22:33:09,540 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 22:33:09,540 - __main__ - INFO - Author: Ethan Li
2025-07-26 22:33:09,540 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 22:33:29,798 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 22:33:29,804 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_223329_L17-IC-filter_1.txt
2025-07-26 22:33:29,805 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_223329_L17-IC-filter_1.txt for user Ethan
2025-07-26 22:33:29,941 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-26 22:33:29,941 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_223329_L17-IC-filter_1.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_223329_L17-IC-filter_1.bin
2025-07-26 22:33:33,445 - __main__ - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 22:33:33,446 - __main__ - WARNING - External validation tool failed or not available
2025-07-26 22:33:33,446 - __main__ - INFO - Analysis complete. No issues found.
2025-07-26 22:33:33,455 - __main__ - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 22:35:43,567 - log_processors - WARNING - File is empty: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-10\test_empty_file0\empty.txt
2025-07-26 22:35:43,572 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-10\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:35:43,761 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-10\test_detect_tc_flash_log0\test_log.txt
2025-07-26 22:35:43,769 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-10\test_detect_pq_config0\test_config.txt
2025-07-26 22:40:58,768 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,773 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,776 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,779 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,782 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,785 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,788 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,789 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:40:58,789 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:40:58,792 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,792 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:40:58,792 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:40:58,796 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,800 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:40:58,831 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:40:58,841 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_execute_grid_comparison_p0\output
2025-07-26 22:40:58,843 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753594858_all_parameters_comparison_grid.png
2025-07-26 22:40:58,852 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:40:58,855 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:40:58,855 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:40:58,879 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:40:58,885 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-11\test_processor_recommendation0\test_log.txt
2025-07-26 22:43:20,546 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,548 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,550 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,551 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,554 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,556 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,558 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,559 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:43:20,559 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:43:20,562 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,562 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:43:20,562 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:43:20,563 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:20,565 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:43:25,570 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:43:25,578 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_execute_grid_comparison_p0\output
2025-07-26 22:43:25,580 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753595005_all_parameters_comparison_grid.png
2025-07-26 22:43:25,587 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:43:25,590 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:43:25,590 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:43:25,608 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:43:25,614 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-12\test_processor_recommendation0\test_log.txt
2025-07-26 22:47:14,141 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,144 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,147 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,149 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,153 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,156 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,158 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,159 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 22:47:14,159 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 22:47:14,161 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,161 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 22:47:14,161 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 22:47:14,163 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:14,165 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 22:47:20,411 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_nonexistent_file0\nonexistent.txt
2025-07-26 22:47:20,423 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_execute_grid_comparison_p0\output
2025-07-26 22:47:20,426 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753595240_all_parameters_comparison_grid.png
2025-07-26 22:47:20,435 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 22:47:20,438 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 22:47:20,438 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 22:47:20,459 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_tc_flash_log_detection0\test_log.txt
2025-07-26 22:47:20,466 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-13\test_processor_recommendation0\test_log.txt
2025-07-26 23:00:33,497 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-14\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-14\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:00:33,499 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753596033_L17-IC-filter_0.0416.png
2025-07-26 23:02:16,519 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:02:16,530 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_grid_comparison_p0\output
2025-07-26 23:02:16,533 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753596136_all_parameters_comparison_grid.png
2025-07-26 23:02:16,541 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 23:02:16,543 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 23:02:16,544 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 23:02:16,550 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:02:16,553 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753596136_L17-IC-filter_0.0416.png
2025-07-26 23:02:16,581 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:02:16,589 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-18\test_processor_recommendation0\test_log.txt
2025-07-26 23:03:35,768 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,770 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,772 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,776 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,779 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,782 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,784 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,784 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 23:03:35,784 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 23:03:35,786 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,786 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 23:03:35,786 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 23:03:35,788 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:35,790 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:03:41,482 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:03:41,493 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_grid_comparison_p0\output
2025-07-26 23:03:41,496 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753596221_all_parameters_comparison_grid.png
2025-07-26 23:03:41,503 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 23:03:41,505 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 23:03:41,506 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 23:03:41,514 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:03:41,516 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753596221_L17-IC-filter_0.0416.png
2025-07-26 23:03:41,542 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:03:41,548 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-19\test_processor_recommendation0\test_log.txt
2025-07-26 23:04:15,880 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,883 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,886 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,888 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,890 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,892 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,894 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,894 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 23:04:15,894 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 23:04:15,897 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,898 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 23:04:15,898 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 23:04:15,901 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:15,903 - web_app_registry - INFO - Loaded 6 web applications
2025-07-26 23:04:21,389 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:04:21,397 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_grid_comparison_p0\output
2025-07-26 23:04:21,400 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753596261_all_parameters_comparison_grid.png
2025-07-26 23:04:21,411 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\log_parser_plotter.py
2025-07-26 23:04:21,412 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_log_parser_plotte0\output\config_overview.png
2025-07-26 23:04:21,413 - log_processors - WARNING - PNG file not found for copying: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_log_parser_plotte0\output\dm_overview.png
2025-07-26 23:04:21,422 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:04:21,425 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753596261_L17-IC-filter_0.0416.png
2025-07-26 23:04:21,455 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:04:21,460 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-20\test_processor_recommendation0\test_log.txt
2025-07-26 23:05:34,433 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:05:34,434 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:05:34,434 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:06:02,116 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 23:06:02,125 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_230602_L17-IC-filter_1.txt
2025-07-26 23:06:02,127 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_230602_L17-IC-filter_1.txt for user Ethan
2025-07-26 23:06:02,356 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-26 23:06:02,358 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_230602_L17-IC-filter_1.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_230602_L17-IC-filter_1.bin
2025-07-26 23:06:05,887 - __main__ - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 23:06:05,887 - __main__ - WARNING - External validation tool failed or not available
2025-07-26 23:06:05,887 - __main__ - INFO - Analysis complete. No issues found.
2025-07-26 23:06:05,898 - __main__ - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 23:16:40,823 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:16:40,833 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_execute_grid_comparison_p0\output
2025-07-26 23:16:40,837 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597000_all_parameters_comparison_grid.png
2025-07-26 23:16:40,849 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:16:40,852 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753597000_L17-IC-filter_0.0416.png
2025-07-26 23:16:40,882 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:16:40,889 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-21\test_processor_recommendation0\test_log.txt
2025-07-26 23:21:57,008 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,013 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,017 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,020 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,027 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,030 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,033 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,033 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 23:21:57,033 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 23:21:57,036 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,037 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 23:21:57,037 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 23:21:57,041 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,046 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:21:57,073 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:21:57,086 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_execute_grid_comparison_p0\output
2025-07-26 23:21:57,094 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597317_all_parameters_comparison_grid.png
2025-07-26 23:21:57,116 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:21:57,125 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753597317_L17-IC-filter_0.0416.png
2025-07-26 23:21:57,166 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:21:57,177 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-22\test_processor_recommendation0\test_log.txt
2025-07-26 23:22:37,909 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,912 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,917 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,920 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,924 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,927 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,933 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,933 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 23:22:37,934 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 23:22:37,938 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,938 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 23:22:37,938 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 23:22:37,941 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,943 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:22:37,968 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:22:37,978 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_execute_grid_comparison_p0\output
2025-07-26 23:22:37,985 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597357_all_parameters_comparison_grid.png
2025-07-26 23:22:37,992 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:22:37,995 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753597357_L17-IC-filter_0.0416.png
2025-07-26 23:22:38,018 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:22:38,023 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-23\test_processor_recommendation0\test_log.txt
2025-07-26 23:22:55,582 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:22:55,583 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:22:55,583 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:24:37,676 - __main__ - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-26 23:24:37,680 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232437_TC_stranger_things_pq_log.txt
2025-07-26 23:24:37,680 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232437_TC_stranger_things_pq_log.txt for user Ethan
2025-07-26 23:24:37,683 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232437_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_232437
2025-07-26 23:24:37,824 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-07-26 23:24:37,833 - __main__ - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-26 23:25:35,843 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:25:35,844 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:25:35,844 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:25:44,966 - __main__ - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-26 23:25:44,969 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232544_TC_stranger_things_pq_log.txt
2025-07-26 23:25:44,970 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232544_TC_stranger_things_pq_log.txt for user Ethan
2025-07-26 23:25:44,972 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232544_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_232544
2025-07-26 23:25:52,293 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597552_all_parameters_comparison_grid.png
2025-07-26 23:25:52,306 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-26 23:27:03,166 - __main__ - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-26 23:27:03,169 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232703_Wrong_PQcfg.cfg
2025-07-26 23:27:03,169 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232703_Wrong_PQcfg.cfg for user Ethan
2025-07-26 23:27:03,173 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-26 23:27:03,173 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232703_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232703_Wrong_PQcfg.bin
2025-07-26 23:27:03,224 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-26 23:27:03,225 - __main__ - INFO - External tool found 2 validation warnings
2025-07-26 23:27:03,225 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-26 23:27:03,230 - __main__ - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-26 23:27:29,015 - __main__ - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-26 23:27:29,019 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232729_TC_Flash_first_few_frames_pq_log.txt
2025-07-26 23:27:29,020 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232729_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-26 23:27:29,026 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_232729_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_232729
2025-07-26 23:27:36,149 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597656_all_parameters_comparison_grid.png
2025-07-26 23:27:36,175 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-26 23:29:41,131 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:29:41,132 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:29:41,132 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:30:08,343 - __main__ - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-26 23:30:08,347 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233008_TC_stranger_things_pq_log.txt
2025-07-26 23:30:08,348 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233008_TC_stranger_things_pq_log.txt for user Ethan
2025-07-26 23:30:08,351 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233008_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_233008
2025-07-26 23:30:15,927 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753597815_all_parameters_comparison_grid.png
2025-07-26 23:30:15,945 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-26 23:31:42,800 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-26 23:31:42,803 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233142_Wrong_PQcfg.cfg
2025-07-26 23:31:42,805 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233142_Wrong_PQcfg.cfg for user Ethan
2025-07-26 23:31:42,808 - main - INFO - Running external Dolby Vision validation tool
2025-07-26 23:31:42,809 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233142_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233142_Wrong_PQcfg.bin
2025-07-26 23:31:42,867 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-26 23:31:42,867 - main - INFO - External tool found 2 validation warnings
2025-07-26 23:31:42,867 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-26 23:31:42,880 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-26 23:32:00,666 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-26 23:32:00,672 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233200_TC_Flash_first_few_frames_pq_log.txt
2025-07-26 23:32:00,672 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233200_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-26 23:32:00,679 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233200_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_233200
2025-07-26 23:32:00,778 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-07-26 23:32:00,788 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-26 23:33:22,331 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:33:22,343 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_execute_grid_comparison_p0\output
2025-07-26 23:33:22,347 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753598002_all_parameters_comparison_grid.png
2025-07-26 23:33:22,356 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:33:22,359 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753598002_L17-IC-filter_0.0416.png
2025-07-26 23:33:22,389 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:33:22,397 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-24\test_processor_recommendation0\test_log.txt
2025-07-26 23:36:09,286 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:36:09,286 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:36:09,286 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:36:28,572 - __main__ - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-26 23:36:28,579 - file_type_detector - INFO - Detected TC Flash log file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233628_TC_Flash_first_few_frames_pq_log.txt
2025-07-26 23:36:28,579 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233628_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-26 23:36:28,587 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233628_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_233628
2025-07-26 23:36:36,973 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753598196_all_parameters_comparison_grid.png
2025-07-26 23:36:37,002 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-26 23:37:12,467 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 23:37:12,474 - file_type_detector - WARNING - Unknown file type: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233712_L17-IC-filter_1.txt
2025-07-26 23:37:12,474 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233712_L17-IC-filter_1.txt for user Ethan
2025-07-26 23:37:12,621 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-26 23:37:12,624 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233712_L17-IC-filter_1.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_233712_L17-IC-filter_1.bin
2025-07-26 23:37:15,508 - __main__ - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-26 23:37:15,509 - __main__ - WARNING - External validation tool failed or not available
2025-07-26 23:37:15,509 - __main__ - INFO - Analysis complete. No issues found.
2025-07-26 23:37:15,514 - __main__ - INFO - File processing complete: Analysis complete. No issues found.
2025-07-26 23:41:52,009 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:41:52,009 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:41:52,010 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:42:19,856 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-26 23:42:19,901 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234219_L17-IC-filter_1.txt for user Ethan
2025-07-26 23:42:20,085 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234219_L17-IC-filter_1.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_234220 --no-show
2025-07-26 23:42:22,987 - __main__ - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-07-26 23:42:43,635 - __main__ - INFO - Processing upload: L17-IC-filter_0.0416.txt (422.4 KB) for user Ethan
2025-07-26 23:42:43,650 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234243_L17-IC-filter_0.0416.txt for user Ethan
2025-07-26 23:42:43,664 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234243_L17-IC-filter_0.0416.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_234243 --no-show
2025-07-26 23:42:45,620 - __main__ - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-07-26 23:43:09,593 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmpczuj9nhe.txt
2025-07-26 23:44:23,100 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:44:23,101 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:44:23,101 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:44:35,804 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:44:35,804 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:44:35,804 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:44:45,873 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-26 23:44:45,873 - __main__ - INFO - Author: Ethan Li
2025-07-26 23:44:45,873 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-26 23:45:03,091 - __main__ - INFO - Processing upload: L17-IC-filter_0.0416.txt (422.4 KB) for user Ethan
2025-07-26 23:45:03,093 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234503_L17-IC-filter_0.0416.txt for user Ethan
2025-07-26 23:45:03,095 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250726_234503_L17-IC-filter_0.0416.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250726_234503 --no-show
2025-07-26 23:45:10,537 - log_processors - INFO - Copied 20250726_234503_L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753598710_20250726_234503_L17-IC-filter_0.0416.png
2025-07-26 23:45:10,551 - __main__ - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-26 23:52:27,985 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,987 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,990 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,992 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,994 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,995 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,998 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:27,999 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-26 23:52:27,999 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-26 23:52:28,000 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:28,000 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-26 23:52:28,001 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-26 23:52:28,002 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:28,005 - web_app_registry - INFO - Loaded 5 web applications
2025-07-26 23:52:33,291 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_nonexistent_file0\nonexistent.txt
2025-07-26 23:52:33,310 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_execute_grid_comparison_p0\output
2025-07-26 23:52:33,318 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753599153_all_parameters_comparison_grid.png
2025-07-26 23:52:33,328 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_execute_firmware_log_visu0\output --no-show
2025-07-26 23:52:33,333 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753599153_L17-IC-filter_0.0416.png
2025-07-26 23:52:33,360 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_tc_flash_log_detection0\test_log.txt
2025-07-26 23:52:33,369 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-26\test_processor_recommendation0\test_log.txt
2025-07-26 23:53:11,436 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmpp8bj4z82.txt
2025-07-27 00:00:11,428 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-27 00:00:11,429 - __main__ - INFO - Author: Ethan Li
2025-07-27 00:00:11,429 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-27 00:00:30,545 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-27 00:00:30,547 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000030_L17-IC-filter_1.txt for user Ethan
2025-07-27 00:00:30,548 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000030_L17-IC-filter_1.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_000030 --no-show
2025-07-27 00:00:38,572 - log_processors - INFO - Copied 20250727_000030_L17-IC-filter_1.png to static directory: /static/images/firmware_log_visualizer/1753599638_20250727_000030_L17-IC-filter_1.png
2025-07-27 00:00:38,590 - __main__ - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-27 00:01:33,455 - __main__ - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 00:01:33,457 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000133_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 00:01:33,461 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000133_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_000133
2025-07-27 00:01:40,274 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753599700_all_parameters_comparison_grid.png
2025-07-27 00:01:40,299 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 00:03:38,019 - __main__ - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-27 00:03:38,020 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000338_TC_stranger_things_pq_log.txt for user Ethan
2025-07-27 00:03:38,022 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000338_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_000338
2025-07-27 00:03:44,330 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753599824_all_parameters_comparison_grid.png
2025-07-27 00:03:44,344 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 00:04:19,383 - __main__ - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 00:04:19,386 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000419_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 00:04:19,388 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000419_L17-IC-filter_0.5.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_000419 --no-show
2025-07-27 00:04:26,403 - log_processors - INFO - Copied 20250727_000419_L17-IC-filter_0.5.png to static directory: /static/images/firmware_log_visualizer/1753599866_20250727_000419_L17-IC-filter_0.5.png
2025-07-27 00:04:26,422 - __main__ - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-27 00:05:14,685 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000419_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 00:05:14,910 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:05:14,911 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000419_L17-IC-filter_0.5.txt -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000419_L17-IC-filter_0.5.bin
2025-07-27 00:05:18,130 - __main__ - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-27 00:05:18,130 - __main__ - WARNING - External validation tool failed or not available
2025-07-27 00:05:18,130 - __main__ - INFO - Analysis complete. No issues found.
2025-07-27 00:07:37,367 - __main__ - INFO - Processing upload: 55HQ1_FACTORY.cfg (4.2 KB) for user Ethan
2025-07-27 00:07:37,371 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000737_55HQ1_FACTORY.cfg
2025-07-27 00:07:37,371 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000737_55HQ1_FACTORY.cfg for user Ethan
2025-07-27 00:07:37,371 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:07:37,371 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000737_55HQ1_FACTORY.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000737_55HQ1_FACTORY.bin
2025-07-27 00:07:37,451 - __main__ - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-27 00:07:37,451 - __main__ - INFO - External tool found 0 validation warnings
2025-07-27 00:07:37,451 - __main__ - INFO - Analysis complete. No issues found.
2025-07-27 00:07:37,451 - __main__ - INFO - File processing complete: Analysis complete. No issues found.
2025-07-27 00:07:50,589 - __main__ - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 00:07:50,591 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000750_Wrong_PQcfg.cfg
2025-07-27 00:07:50,592 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000750_Wrong_PQcfg.cfg for user Ethan
2025-07-27 00:07:50,595 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:07:50,595 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000750_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000750_Wrong_PQcfg.bin
2025-07-27 00:07:50,647 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 00:07:50,648 - __main__ - INFO - External tool found 2 validation warnings
2025-07-27 00:07:50,648 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 00:07:50,659 - __main__ - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 00:09:49,001 - __main__ - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-07-27 00:09:49,002 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_000949_L17-IC-filter_1.txt for user Ethan
2025-07-27 00:09:49,017 - __main__ - INFO - File processing complete: Grid Comparison Plotter requires a TC Flash PQ log file with Config and DM values sections
2025-07-27 00:10:06,224 - __main__ - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 00:10:06,225 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001006_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 00:10:06,231 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001006_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_001006
2025-07-27 00:10:12,997 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753600212_all_parameters_comparison_grid.png
2025-07-27 00:10:13,026 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 00:17:43,051 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 00:17:43,052 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001743_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 00:17:43,060 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001743_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_001743
2025-07-27 00:17:43,230 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-07-27 00:17:43,243 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-27 00:18:27,562 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-27 00:18:27,562 - __main__ - INFO - Author: Ethan Li
2025-07-27 00:18:27,562 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-27 00:18:58,249 - __main__ - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 00:18:58,250 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001858_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 00:18:58,256 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_001858_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_001858
2025-07-27 00:18:58,361 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-07-27 00:18:58,375 - __main__ - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-27 00:20:49,297 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-27 00:20:49,297 - __main__ - INFO - Author: Ethan Li
2025-07-27 00:20:49,297 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-27 00:21:09,578 - __main__ - INFO - Starting PQ Config Validator v1.0.0
2025-07-27 00:21:09,578 - __main__ - INFO - Author: Ethan Li
2025-07-27 00:21:09,578 - __main__ - INFO - Server will be available at http://0.0.0.0:8000
2025-07-27 00:21:38,052 - __main__ - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-27 00:21:38,052 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002138_TC_stranger_things_pq_log.txt for user Ethan
2025-07-27 00:21:38,052 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002138_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_002138
2025-07-27 00:21:44,994 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753600904_all_parameters_comparison_grid.png
2025-07-27 00:21:45,010 - __main__ - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 00:22:17,145 - __main__ - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 00:22:17,147 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.cfg
2025-07-27 00:22:17,151 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.cfg for user Ethan
2025-07-27 00:22:17,152 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:22:17,152 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.bin
2025-07-27 00:22:17,203 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 00:22:17,203 - __main__ - INFO - External tool found 2 validation warnings
2025-07-27 00:22:17,203 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 00:22:17,223 - __main__ - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 00:22:21,785 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.cfg for user Ethan
2025-07-27 00:22:21,795 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:22:21,795 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002217_Wrong_PQcfg.bin
2025-07-27 00:22:21,839 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 00:22:21,844 - __main__ - INFO - External tool found 2 validation warnings
2025-07-27 00:22:21,844 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 00:22:46,502 - __main__ - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 00:22:46,503 - __main__ - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002246_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 00:22:46,505 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002246_L17-IC-filter_0.5.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_002246 --no-show
2025-07-27 00:22:54,433 - log_processors - INFO - Copied 20250727_002246_L17-IC-filter_0.5.png to static directory: /static/images/firmware_log_visualizer/1753600974_20250727_002246_L17-IC-filter_0.5.png
2025-07-27 00:22:54,447 - __main__ - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-27 00:23:28,325 - __main__ - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 00:23:28,327 - file_type_detector - INFO - Detected PQ config file: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002328_Wrong_PQcfg.cfg
2025-07-27 00:23:28,327 - __main__ - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002328_Wrong_PQcfg.cfg for user Ethan
2025-07-27 00:23:28,330 - __main__ - INFO - Running external Dolby Vision validation tool
2025-07-27 00:23:28,331 - __main__ - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002328_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_002328_Wrong_PQcfg.bin
2025-07-27 00:23:28,382 - __main__ - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 00:23:28,382 - __main__ - INFO - External tool found 2 validation warnings
2025-07-27 00:23:28,382 - __main__ - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 00:23:28,393 - __main__ - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 00:50:16,375 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,781 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,785 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,788 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,790 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,794 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,797 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,988 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:45,988 - web_app_registry - ERROR - Failed to register application dynamic_app: missing 'icon'
2025-07-27 00:50:46,001 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:46,001 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 00:50:46,001 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 00:50:46,003 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:50:46,005 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,359 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,361 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,364 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,367 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,370 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,372 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,374 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,375 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 00:51:42,375 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 00:51:42,377 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,377 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 00:51:42,377 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 00:51:42,379 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:42,382 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,166 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmph7i1qicc.txt
2025-07-27 00:51:53,421 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpq7a7vuy6.txt
2025-07-27 00:51:53,523 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 00:51:53,526 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 00:51:53,573 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp3pzjjnzg\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp3pzjjnzg\output
2025-07-27 00:51:53,786 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,790 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,793 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,800 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,803 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,807 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,810 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,810 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 00:51:53,810 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 00:51:53,813 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,813 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 00:51:53,813 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 00:51:53,817 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 00:51:53,820 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:00:11,456 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpnf0v4o_t.txt
2025-07-27 01:00:28,315 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpd3jmf66j.txt
2025-07-27 01:01:29,109 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmp62cmc5pf.txt
2025-07-27 01:01:29,119 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpidpnkklq.txt
2025-07-27 01:01:29,125 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmphxt6otbq.txt
2025-07-27 01:01:29,141 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 01:01:29,145 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 01:01:29,158 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp5c7gj47x\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp5c7gj47x\output
2025-07-27 01:01:29,353 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,360 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,364 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,371 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,377 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,385 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,394 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,394 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 01:01:29,394 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 01:01:29,402 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,403 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 01:01:29,403 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 01:01:29,408 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:29,412 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:42,317 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_nonexistent_file0\nonexistent.txt
2025-07-27 01:01:42,332 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_execute_grid_comparison_p0\output
2025-07-27 01:01:42,335 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753603302_all_parameters_comparison_grid.png
2025-07-27 01:01:42,344 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_execute_firmware_log_visu0\output --no-show
2025-07-27 01:01:42,347 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753603302_L17-IC-filter_0.0416.png
2025-07-27 01:01:42,379 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_tc_flash_log_detection0\test_log.txt
2025-07-27 01:01:42,388 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-27\test_processor_recommendation0\test_log.txt
2025-07-27 01:01:54,715 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmp0n0ivl6i.txt
2025-07-27 01:01:54,825 - httpx - INFO - HTTP Request: POST http://testserver/api/register "HTTP/1.1 404 Not Found"
2025-07-27 01:01:55,056 - httpx - INFO - HTTP Request: POST http://testserver/api/register "HTTP/1.1 404 Not Found"
2025-07-27 01:01:55,116 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpj4tdnxu7.txt
2025-07-27 01:01:55,125 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmp1q1dpw6e.txt
2025-07-27 01:01:55,133 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmp9v4kcs7r.txt
2025-07-27 01:01:55,144 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 01:01:55,150 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 01:01:55,164 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp7z4wwy83\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp7z4wwy83\output
2025-07-27 01:01:55,429 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,435 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,438 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,442 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,446 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,449 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,454 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,455 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 01:01:55,455 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 01:01:55,458 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,458 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 01:01:55,459 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 01:01:55,462 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:01:55,467 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:04:55,910 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:05:55,439 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:05:55,459 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-27 01:05:55,460 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_grid_ui_user\20250727_010555_test_log.txt for user test_grid_ui_user
2025-07-27 01:05:55,471 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-27 01:05:55,474 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/6d4d2ec9-6999-4e7f-981d-83953ae350ac "HTTP/1.1 200 OK"
2025-07-27 01:05:55,722 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:05:55,736 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-27 01:05:55,737 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_firmware_ui_user\20250727_010555_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-27 01:05:55,751 - main - INFO - File processing complete: Firmware log visualization generated successfully
2025-07-27 01:05:55,753 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/3c0df081-7f33-4855-8966-ebb84cff7496 "HTTP/1.1 200 OK"
2025-07-27 01:07:31,853 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmptou7m2i2.txt
2025-07-27 01:07:31,915 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:07:31,946 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-27 01:07:31,947 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_grid_ui_user\20250727_010731_test_log.txt for user test_grid_ui_user
2025-07-27 01:07:31,972 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-27 01:07:31,974 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/d71b68da-3379-490d-aa0d-ad0c61ef0668 "HTTP/1.1 200 OK"
2025-07-27 01:07:32,005 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:07:32,023 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-27 01:07:32,024 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_firmware_ui_user\20250727_010732_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-27 01:07:32,042 - main - INFO - File processing complete: Firmware log visualization generated successfully
2025-07-27 01:07:32,043 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/0c662871-0e45-4b4e-8ef2-da51597c0363 "HTTP/1.1 200 OK"
2025-07-27 01:07:43,995 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_nonexistent_file0\nonexistent.txt
2025-07-27 01:07:44,009 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_execute_grid_comparison_p0\output
2025-07-27 01:07:44,013 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753603664_all_parameters_comparison_grid.png
2025-07-27 01:07:44,025 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_execute_firmware_log_visu0\output --no-show
2025-07-27 01:07:44,027 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753603664_L17-IC-filter_0.0416.png
2025-07-27 01:07:44,056 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_tc_flash_log_detection0\test_log.txt
2025-07-27 01:07:44,067 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-28\test_processor_recommendation0\test_log.txt
2025-07-27 01:07:56,431 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmp6dtauj29.txt
2025-07-27 01:07:56,566 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:07:56,601 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-27 01:07:56,601 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_grid_ui_user\20250727_010756_test_log.txt for user test_grid_ui_user
2025-07-27 01:07:56,620 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-27 01:07:56,623 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/24116314-41d9-467b-b452-abe1e80b2a37 "HTTP/1.1 200 OK"
2025-07-27 01:07:56,640 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:07:56,655 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-27 01:07:56,655 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_firmware_ui_user\20250727_010756_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-27 01:07:56,662 - main - INFO - File processing complete: Firmware log visualization generated successfully
2025-07-27 01:07:56,662 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/161effc1-a699-4393-a584-f6581fe889d0 "HTTP/1.1 200 OK"
2025-07-27 01:07:56,719 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmp28hx8zg0.txt
2025-07-27 01:07:56,728 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpdc8qqz5m.txt
2025-07-27 01:07:56,735 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpdz71z13p.txt
2025-07-27 01:07:56,747 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 01:07:56,749 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 01:07:56,760 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp6uvgiavc\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp6uvgiavc\output
2025-07-27 01:07:56,906 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,908 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,911 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,914 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,919 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,923 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,928 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,928 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 01:07:56,929 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 01:07:56,932 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,932 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 01:07:56,932 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 01:07:56,939 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:07:56,941 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:44,427 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_nonexistent_file0\nonexistent.txt
2025-07-27 01:13:44,442 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_execute_grid_comparison_p0\output
2025-07-27 01:13:44,443 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753604024_all_parameters_comparison_grid.png
2025-07-27 01:13:44,456 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_execute_firmware_log_visu0\output --no-show
2025-07-27 01:13:44,460 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753604024_L17-IC-filter_0.0416.png
2025-07-27 01:13:44,532 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_tc_flash_log_detection0\test_log.txt
2025-07-27 01:13:44,547 - file_type_detector - INFO - Detected TC Flash log file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-29\test_processor_recommendation0\test_log.txt
2025-07-27 01:13:56,918 - file_type_detector - INFO - Detected PQ config file: C:\Users\<USER>\AppData\Local\Temp\tmpkyrt68pg.txt
2025-07-27 01:13:56,963 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:13:56,979 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-27 01:13:56,980 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_grid_ui_user\20250727_011356_test_log.txt for user test_grid_ui_user
2025-07-27 01:13:56,992 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-27 01:13:56,994 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/6854ef53-d406-4749-ac0b-39d5d3772e01 "HTTP/1.1 200 OK"
2025-07-27 01:13:57,010 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-27 01:13:57,022 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-27 01:13:57,023 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\test_firmware_ui_user\20250727_011357_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-27 01:13:57,032 - main - INFO - File processing complete: Firmware log visualization generated successfully
2025-07-27 01:13:57,034 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/f58b270f-df20-4769-a682-e5c287f8ec39 "HTTP/1.1 200 OK"
2025-07-27 01:13:57,075 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmplyqhewqw.txt
2025-07-27 01:13:57,080 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmp_0t2bo0r.txt
2025-07-27 01:13:57,087 - file_type_detector - WARNING - Unknown file type: C:\Users\<USER>\AppData\Local\Temp\tmpq8g59eih.txt
2025-07-27 01:13:57,100 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 01:13:57,103 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 01:13:57,116 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp6ec67skj\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp6ec67skj\output
2025-07-27 01:13:57,283 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,286 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,291 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,294 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,298 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,302 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,305 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,305 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-27 01:13:57,305 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-27 01:13:57,308 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,308 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-27 01:13:57,308 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-27 01:13:57,312 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:13:57,315 - web_app_registry - INFO - Loaded 5 web applications
2025-07-27 01:16:49,917 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 01:16:49,918 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_011649_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 01:16:49,942 - main - INFO - File processing complete: Grid Comparison Plotter requires a TC Flash PQ log file with Config and DM values sections
2025-07-27 01:16:58,097 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 01:16:58,099 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_011658_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 01:16:58,104 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_011658_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_011658
2025-07-27 01:17:07,387 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753604227_all_parameters_comparison_grid.png
2025-07-27 01:17:07,427 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 01:17:58,457 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 01:17:58,458 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_011758_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 01:17:58,460 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_011758_L17-IC-filter_0.5.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_011758 --no-show
2025-07-27 01:18:07,475 - log_processors - INFO - Copied 20250727_011758_L17-IC-filter_0.5.png to static directory: /static/images/firmware_log_visualizer/1753604287_20250727_011758_L17-IC-filter_0.5.png
2025-07-27 01:18:07,496 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-27 10:10:12,176 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-27 10:10:26,199 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-27 10:12:06,150 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-30\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-27 10:12:21,534 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 10:12:21,539 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 10:12:21,634 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-27 10:12:21,653 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp0z0etw0g\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp0z0etw0g\output
2025-07-27 10:21:02,578 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 10:21:02,584 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 10:21:02,624 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-27 10:21:02,639 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmpj86aekvq\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmpj86aekvq\output
2025-07-27 10:21:12,149 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_nonexistent_file0\nonexistent.txt
2025-07-27 10:21:12,192 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-27 10:21:12,204 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_execute_grid_comparison_p0\output
2025-07-27 10:21:12,208 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753636872_all_parameters_comparison_grid.png
2025-07-27 10:21:12,217 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-31\test_execute_firmware_log_visu0\output --no-show
2025-07-27 10:21:12,221 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753636872_L17-IC-filter_0.0416.png
2025-07-27 10:22:11,186 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_nonexistent_file0\nonexistent.txt
2025-07-27 10:22:11,225 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-27 10:22:11,237 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_execute_grid_comparison_p0\output
2025-07-27 10:22:11,242 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753636931_all_parameters_comparison_grid.png
2025-07-27 10:22:11,251 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-34\test_execute_firmware_log_visu0\output --no-show
2025-07-27 10:22:11,253 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753636931_L17-IC-filter_0.0416.png
2025-07-27 10:22:48,356 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-27 10:22:48,360 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-27 10:22:48,405 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-27 10:22:48,422 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmpcuo1chsd\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmpcuo1chsd\output
2025-07-27 10:22:48,468 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_nonexistent_file0\nonexistent.txt
2025-07-27 10:22:48,499 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-27 10:22:48,510 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_execute_grid_comparison_p0\output
2025-07-27 10:22:48,513 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753636968_all_parameters_comparison_grid.png
2025-07-27 10:22:48,521 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-35\test_execute_firmware_log_visu0\output --no-show
2025-07-27 10:22:48,524 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753636968_L17-IC-filter_0.0416.png
2025-07-27 10:50:09,321 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-27 10:50:09,323 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105009_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-27 10:50:09,331 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105009_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_105009
2025-07-27 10:50:17,603 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753638617_all_parameters_comparison_grid.png
2025-07-27 10:50:17,655 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 10:50:55,425 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 10:50:55,427 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105055_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 10:50:55,491 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\firmware_log_visualizer.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105055_L17-IC-filter_0.5.txt --output D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_105055 --no-show
2025-07-27 10:51:03,359 - log_processors - INFO - Copied 20250727_105055_L17-IC-filter_0.5.png to static directory: /static/images/firmware_log_visualizer/1753638663_20250727_105055_L17-IC-filter_0.5.png
2025-07-27 10:51:03,374 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-27 10:51:28,125 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 10:51:28,125 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105128_Wrong_PQcfg.cfg for user Ethan
2025-07-27 10:51:28,132 - main - INFO - Running external Dolby Vision validation tool
2025-07-27 10:51:28,132 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105128_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105128_Wrong_PQcfg.bin
2025-07-27 10:51:28,198 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 10:51:28,199 - main - INFO - External tool found 2 validation warnings
2025-07-27 10:51:28,199 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 10:51:28,215 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 10:55:55,767 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-27 10:55:55,769 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105555_L17-IC-filter_0.5.txt for user Ethan
2025-07-27 10:55:55,785 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-07-27 10:56:10,622 - main - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-27 10:56:10,623 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105610_TC_stranger_things_pq_log.txt for user Ethan
2025-07-27 10:56:10,627 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\bin\grid_comparison_plotter.py D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\20250727_105610_TC_stranger_things_pq_log.txt --output-dir D:\PycharmProjects\pq_tuning_config_editor\prototypes\detect_undef_pq_para\uploads\Ethan\log_outputs\20250727_105610
2025-07-27 10:56:17,052 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753638977_all_parameters_comparison_grid.png
2025-07-27 10:56:17,077 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-27 11:08:24,068 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:09:07,314 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:14:53,775 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:15:31,388 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 11:15:31,390 - main - INFO - Processing file D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\Ethan\20250727_111531_Wrong_PQcfg.cfg for user Ethan
2025-07-27 11:15:31,397 - main - INFO - Running external Dolby Vision validation tool
2025-07-27 11:15:31,397 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\Ethan\20250727_111531_Wrong_PQcfg.cfg -b D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\Ethan\20250727_111531_Wrong_PQcfg.bin
2025-07-27 11:15:31,455 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 11:15:31,455 - main - INFO - External tool found 2 validation warnings
2025-07-27 11:15:31,455 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 11:15:31,471 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 11:19:24,314 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:22:28,839 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:23:19,614 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-07-27 11:23:57,803 - main - INFO - Processing upload: Wrong_PQcfg.cfg (3.2 KB) for user Ethan
2025-07-27 11:23:57,803 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250727_112357_Wrong_PQcfg.cfg for user Ethan
2025-07-27 11:23:57,803 - main - INFO - Running external Dolby Vision validation tool
2025-07-27 11:23:57,803 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250727_112357_Wrong_PQcfg.cfg -b D:\dvv_uploads\Ethan\20250727_112357_Wrong_PQcfg.bin
2025-07-27 11:23:57,859 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-27 11:23:57,859 - main - INFO - External tool found 2 validation warnings
2025-07-27 11:23:57,859 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-27 11:23:57,875 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-27 11:26:24,156 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:29:19,853 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-07-27 11:39:40,592 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:39:50,307 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-27 11:40:07,413 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 10:43:37,495 - main - INFO - Processing upload: DOLBY_FACTORY-beta6-1.cfg (6.4 KB) for user Jas_PQParTest_1
2025-07-28 10:43:37,496 - main - INFO - Processing file D:\dvv_uploads\Jas_PQParTest_1\20250728_104337_DOLBY_FACTORY-beta6-1.cfg for user Jas_PQParTest_1
2025-07-28 10:43:37,501 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 10:43:37,502 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Jas_PQParTest_1\20250728_104337_DOLBY_FACTORY-beta6-1.cfg -b D:\dvv_uploads\Jas_PQParTest_1\20250728_104337_DOLBY_FACTORY-beta6-1.bin
2025-07-28 10:43:37,557 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-07-28 10:43:37,558 - main - INFO - External tool found 5 validation warnings
2025-07-28 10:43:37,558 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-07-28 10:43:37,571 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-07-28 12:05:38,653 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_1_L15L17.txt (149.6 KB) for user Ethan
2025-07-28 12:05:38,654 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_120538_DVSDK_7353_IC10_filter_1_L15L17.txt for user Ethan
2025-07-28 12:05:38,655 - main - ERROR - Unexpected error processing log file: name 'LOG_PROCESSING_ENABLED' is not defined
2025-07-28 12:05:38,665 - main - INFO - File processing complete: Unexpected error processing log file: name 'LOG_PROCESSING_ENABLED' is not defined
2025-07-28 12:08:15,506 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-07-28 12:08:24,546 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_1_L15L17.txt (149.6 KB) for user Ethan
2025-07-28 12:08:24,547 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_120824_DVSDK_7353_IC10_filter_1_L15L17.txt for user Ethan
2025-07-28 12:08:24,572 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250728_120824_DVSDK_7353_IC10_filter_1_L15L17.txt --output D:\dvv_uploads\Ethan\log_outputs\20250728_120824 --no-show
2025-07-28 12:08:32,289 - log_processors - INFO - Copied 20250728_120824_DVSDK_7353_IC10_filter_1_L15L17.png to static directory: /static/images/firmware_log_visualizer/1753729712_20250728_120824_DVSDK_7353_IC10_filter_1_L15L17.png
2025-07-28 12:08:32,312 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 12:09:13,167 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_0.01_L15L17.txt (86.7 KB) for user Ethan
2025-07-28 12:09:13,169 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_120913_DVSDK_7353_IC10_filter_0.01_L15L17.txt for user Ethan
2025-07-28 12:09:13,182 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250728_120913_DVSDK_7353_IC10_filter_0.01_L15L17.txt --output D:\dvv_uploads\Ethan\log_outputs\20250728_120913 --no-show
2025-07-28 12:09:19,691 - log_processors - INFO - Copied 20250728_120913_DVSDK_7353_IC10_filter_0.01_L15L17.png to static directory: /static/images/firmware_log_visualizer/1753729759_20250728_120913_DVSDK_7353_IC10_filter_0.01_L15L17.png
2025-07-28 12:09:19,706 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 12:47:15,833 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-28 12:47:15,834 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_124715_pq_log.txt for user cshiv
2025-07-28 12:47:15,837 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_124715_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_124715
2025-07-28 12:47:22,034 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753732042_all_parameters_comparison_grid.png
2025-07-28 12:47:22,045 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 12:48:44,392 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-28 12:48:44,397 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_124844_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-28 12:48:44,406 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250728_124844_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250728_124844
2025-07-28 12:48:50,649 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753732130_all_parameters_comparison_grid.png
2025-07-28 12:48:50,697 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 12:52:09,520 - main - INFO - Processing upload: L17_1795_PQ.txt (6.2 KB) for user Ethan
2025-07-28 12:52:09,520 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250728_125209_L17_1795_PQ.txt for user Ethan
2025-07-28 12:52:09,527 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 12:52:09,528 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250728_125209_L17_1795_PQ.txt -b D:\dvv_uploads\Ethan\20250728_125209_L17_1795_PQ.bin
2025-07-28 12:52:09,590 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-07-28 12:52:09,591 - main - INFO - External tool found 1 validation warnings
2025-07-28 12:52:09,591 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-28 12:52:09,607 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-28 12:52:18,819 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ_updated.txt (6.2 KB) for user Ethan
2025-07-28 12:52:18,820 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250728_125218_2025_07_21_17_22_34_PQ_updated.txt for user Ethan
2025-07-28 12:52:18,825 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 12:52:18,825 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250728_125218_2025_07_21_17_22_34_PQ_updated.txt -b D:\dvv_uploads\Ethan\20250728_125218_2025_07_21_17_22_34_PQ_updated.bin
2025-07-28 12:52:18,866 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-28 12:52:18,866 - main - INFO - External tool found 0 validation warnings
2025-07-28 12:52:18,866 - main - INFO - Analysis complete. No issues found.
2025-07-28 12:52:18,878 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-28 13:00:40,958 - main - INFO - Processing upload: pq_log_original.txt (91.5 KB) for user cshiv
2025-07-28 13:00:40,959 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_130040_pq_log_original.txt for user cshiv
2025-07-28 13:00:40,961 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_130040_pq_log_original.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_130040
2025-07-28 13:00:46,565 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753732846_all_parameters_comparison_grid.png
2025-07-28 13:00:46,584 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 13:19:11,247 - main - INFO - Processing upload: pq_log_setting_initial_IC.txt (91.5 KB) for user cshiv
2025-07-28 13:19:11,249 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_131911_pq_log_setting_initial_IC.txt for user cshiv
2025-07-28 13:19:11,252 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_131911_pq_log_setting_initial_IC.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_131911
2025-07-28 13:19:16,761 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753733956_all_parameters_comparison_grid.png
2025-07-28 13:19:16,774 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 13:53:31,302 - main - INFO - Processing upload: L17_1795_PQ.cfg (6.2 KB) for user Ethan
2025-07-28 13:53:31,302 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250728_135331_L17_1795_PQ.cfg for user Ethan
2025-07-28 13:53:31,305 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 13:53:31,306 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250728_135331_L17_1795_PQ.cfg -b D:\dvv_uploads\Ethan\20250728_135331_L17_1795_PQ.bin
2025-07-28 13:53:31,348 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-07-28 13:53:31,348 - main - INFO - External tool found 1 validation warnings
2025-07-28 13:53:31,349 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-07-28 13:53:31,358 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-07-28 13:54:57,389 - main - INFO - Processing upload: 2025_07_21_17_22_34_PQ_updated.txt (6.2 KB) for user Ethan
2025-07-28 13:54:57,390 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250728_135457_2025_07_21_17_22_34_PQ_updated.txt for user Ethan
2025-07-28 13:54:57,393 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 13:54:57,394 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250728_135457_2025_07_21_17_22_34_PQ_updated.txt -b D:\dvv_uploads\Ethan\20250728_135457_2025_07_21_17_22_34_PQ_updated.bin
2025-07-28 13:54:57,438 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-07-28 13:54:57,438 - main - INFO - External tool found 0 validation warnings
2025-07-28 13:54:57,438 - main - INFO - Analysis complete. No issues found.
2025-07-28 13:54:57,447 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-07-28 13:56:18,084 - main - INFO - Processing upload: L17_1795_PQ_wrong_para.txt (6.2 KB) for user Ethan
2025-07-28 13:56:18,085 - main - INFO - Processing file D:\dvv_uploads\Ethan\20250728_135618_L17_1795_PQ_wrong_para.txt for user Ethan
2025-07-28 13:56:18,089 - main - INFO - Running external Dolby Vision validation tool
2025-07-28 13:56:18,090 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Ethan\20250728_135618_L17_1795_PQ_wrong_para.txt -b D:\dvv_uploads\Ethan\20250728_135618_L17_1795_PQ_wrong_para.bin
2025-07-28 13:56:18,131 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-07-28 13:56:18,132 - main - INFO - External tool found 2 validation warnings
2025-07-28 13:56:18,132 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-07-28 13:56:18,140 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-07-28 13:57:45,777 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (422.4 KB) for user Ethan
2025-07-28 13:57:45,778 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_135745_L17-IC-filter_0.0416.txt for user Ethan
2025-07-28 13:57:45,821 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250728_135745_L17-IC-filter_0.0416.txt --output D:\dvv_uploads\Ethan\log_outputs\20250728_135745 --no-show
2025-07-28 13:57:51,524 - log_processors - INFO - Copied 20250728_135745_L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753736271_20250728_135745_L17-IC-filter_0.0416.png
2025-07-28 13:57:51,542 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 14:00:29,820 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-07-28 14:00:29,821 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_140029_L17-IC-filter_0.5.txt for user Ethan
2025-07-28 14:00:29,865 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250728_140029_L17-IC-filter_0.5.txt --output D:\dvv_uploads\Ethan\log_outputs\20250728_140029 --no-show
2025-07-28 14:00:35,194 - log_processors - INFO - Copied 20250728_140029_L17-IC-filter_0.5.png to static directory: /static/images/firmware_log_visualizer/1753736435_20250728_140029_L17-IC-filter_0.5.png
2025-07-28 14:00:35,211 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 14:05:12,170 - main - INFO - Processing upload: TC_Flash_first_few_frames_pq_log.txt (400.2 KB) for user Ethan
2025-07-28 14:05:12,172 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_140512_TC_Flash_first_few_frames_pq_log.txt for user Ethan
2025-07-28 14:05:12,177 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250728_140512_TC_Flash_first_few_frames_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250728_140512
2025-07-28 14:05:17,608 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753736717_all_parameters_comparison_grid.png
2025-07-28 14:05:17,638 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 14:07:12,042 - main - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-07-28 14:07:12,043 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_140712_TC_stranger_things_pq_log.txt for user Ethan
2025-07-28 14:07:12,046 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250728_140712_TC_stranger_things_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250728_140712
2025-07-28 14:07:17,284 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753736837_all_parameters_comparison_grid.png
2025-07-28 14:07:17,310 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 14:46:58,557 - main - INFO - Processing upload: pq_log_original.txt (91.5 KB) for user cshiv
2025-07-28 14:46:58,558 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_144658_pq_log_original.txt for user cshiv
2025-07-28 14:46:58,561 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_144658_pq_log_original.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_144658
2025-07-28 14:47:04,366 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753739224_all_parameters_comparison_grid.png
2025-07-28 14:47:04,378 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 15:01:56,633 - main - INFO - Processing upload: Log_IC0-10_filter_0.0416.txt (192.6 KB) for user kennywu
2025-07-28 15:01:56,633 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_150156_Log_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 15:01:56,663 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_150156_Log_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_150156 --no-show
2025-07-28 15:02:03,610 - log_processors - INFO - Copied 20250728_150156_Log_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753740123_20250728_150156_Log_IC0-10_filter_0.0416.png
2025-07-28 15:02:03,629 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 15:05:36,137 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-28 15:05:36,138 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_150536_pq_log.txt for user cshiv
2025-07-28 15:05:36,141 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_150536_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_150536
2025-07-28 15:05:42,560 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753740342_all_parameters_comparison_grid.png
2025-07-28 15:05:42,575 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 15:20:13,140 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-28 15:20:13,140 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250728_152013_pq_log.txt for user cshiv
2025-07-28 15:20:13,144 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250728_152013_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250728_152013
2025-07-28 15:20:18,971 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753741218_all_parameters_comparison_grid.png
2025-07-28 15:20:18,990 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 15:31:39,334 - main - INFO - Processing upload: L17-IC10-1.9.txt (67.8 KB) for user kennywu
2025-07-28 15:31:39,335 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_153139_L17-IC10-1.9.txt for user kennywu
2025-07-28 15:31:39,350 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_153139_L17-IC10-1.9.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_153139 --no-show
2025-07-28 15:31:45,281 - log_processors - INFO - Copied 20250728_153139_L17-IC10-1.9.png to static directory: /static/images/firmware_log_visualizer/1753741905_20250728_153139_L17-IC10-1.9.png
2025-07-28 15:31:45,302 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 15:32:30,354 - main - INFO - Processing upload: L17-IC5-1.9.txt (61.9 KB) for user kennywu
2025-07-28 15:32:30,354 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_153230_L17-IC5-1.9.txt for user kennywu
2025-07-28 15:32:30,379 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_153230_L17-IC5-1.9.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_153230 --no-show
2025-07-28 15:32:36,089 - log_processors - INFO - Copied 20250728_153230_L17-IC5-1.9.png to static directory: /static/images/firmware_log_visualizer/1753741956_20250728_153230_L17-IC5-1.9.png
2025-07-28 15:32:36,102 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 16:55:49,112 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (179.9 KB) for user kennywu
2025-07-28 16:55:49,113 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_165549_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 16:55:49,143 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_165549_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_165549 --no-show
2025-07-28 16:55:54,797 - log_processors - INFO - Copied 20250728_165549_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753746954_20250728_165549_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 16:55:54,811 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 16:56:30,788 - main - INFO - Processing upload: Log_IC0-10_filter_0.0416.txt (192.6 KB) for user kennywu
2025-07-28 16:56:30,789 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_165630_Log_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 16:56:30,817 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_165630_Log_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_165630 --no-show
2025-07-28 16:56:36,357 - log_processors - INFO - Copied 20250728_165630_Log_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753746996_20250728_165630_Log_IC0-10_filter_0.0416.png
2025-07-28 16:56:36,374 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 17:07:52,294 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (169.9 KB) for user kennywu
2025-07-28 17:07:52,295 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_170752_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 17:07:52,322 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_170752_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_170752 --no-show
2025-07-28 17:07:57,853 - log_processors - INFO - Copied 20250728_170752_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753747677_20250728_170752_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 17:07:57,869 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 17:08:42,370 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (169.9 KB) for user kennywu
2025-07-28 17:08:42,371 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_170842_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 17:08:42,394 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_170842_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_170842 --no-show
2025-07-28 17:08:48,002 - log_processors - INFO - Copied 20250728_170842_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753747727_20250728_170842_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 17:08:48,023 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 17:09:46,118 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (169.9 KB) for user kennywu
2025-07-28 17:09:46,119 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_170946_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 17:09:46,141 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_170946_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_170946 --no-show
2025-07-28 17:09:52,171 - log_processors - INFO - Copied 20250728_170946_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753747792_20250728_170946_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 17:09:52,188 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 17:10:54,011 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (169.9 KB) for user kennywu
2025-07-28 17:10:54,012 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_171054_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 17:10:54,034 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_171054_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_171054 --no-show
2025-07-28 17:10:59,665 - log_processors - INFO - Copied 20250728_171054_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753747859_20250728_171054_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 17:10:59,678 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 17:14:01,869 - main - INFO - Processing upload: Log_ST_IC0-10_filter_0.0416.txt (417.5 KB) for user kennywu
2025-07-28 17:14:01,870 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250728_171401_Log_ST_IC0-10_filter_0.0416.txt for user kennywu
2025-07-28 17:14:01,915 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250728_171401_Log_ST_IC0-10_filter_0.0416.txt --output D:\dvv_uploads\kennywu\log_outputs\20250728_171401 --no-show
2025-07-28 17:14:07,813 - log_processors - INFO - Copied 20250728_171401_Log_ST_IC0-10_filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753748047_20250728_171401_Log_ST_IC0-10_filter_0.0416.png
2025-07-28 17:14:07,832 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-28 18:04:18,223 - main - INFO - Processing upload: TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:04:18,224 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_180418_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt for user ez
2025-07-28 18:04:18,228 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\ez\20250728_180418_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt --output-dir D:\dvv_uploads\ez\log_outputs\20250728_180418
2025-07-28 18:04:19,480 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: 
2025-07-28 18:04:19,496 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-28 18:13:37,810 - main - INFO - Processing upload: TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:13:37,811 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_181337_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt for user ez
2025-07-28 18:13:37,817 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\ez\20250728_181337_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt --output-dir D:\dvv_uploads\ez\log_outputs\20250728_181337
2025-07-28 18:13:38,965 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: 
2025-07-28 18:13:38,978 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-28 18:14:12,901 - main - INFO - Processing upload: TC8_OTT_L15_noPR_comm_fb_mingaincurves_TV4_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:14:12,902 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_181412_TC8_OTT_L15_noPR_comm_fb_mingaincurves_TV4_pq_log.txt for user ez
2025-07-28 18:14:12,905 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\ez\20250728_181412_TC8_OTT_L15_noPR_comm_fb_mingaincurves_TV4_pq_log.txt --output-dir D:\dvv_uploads\ez\log_outputs\20250728_181412
2025-07-28 18:14:14,133 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: 
2025-07-28 18:14:14,148 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-28 18:15:01,823 - main - INFO - Processing upload: TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:15:01,824 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_181501_TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt for user ez
2025-07-28 18:15:01,840 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-07-28 18:15:03,323 - main - INFO - Processing upload: TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:15:03,324 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_181503_TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt for user ez
2025-07-28 18:15:03,335 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-07-28 18:15:14,525 - main - INFO - Processing upload: TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt (61.4 KB) for user ez
2025-07-28 18:15:14,526 - main - INFO - Processing log file D:\dvv_uploads\ez\20250728_181514_TC36_OTT_L15only_cornerb_TV4_24fps_pq_log.txt for user ez
2025-07-28 18:15:14,536 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-07-28 18:20:22,072 - main - INFO - Processing upload: Beta6_beta5_diff_case_1_pq_log.txt (192.1 KB) for user EZ
2025-07-28 18:20:22,073 - main - INFO - Processing log file D:\dvv_uploads\EZ\20250728_182022_Beta6_beta5_diff_case_1_pq_log.txt for user EZ
2025-07-28 18:20:22,076 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\EZ\20250728_182022_Beta6_beta5_diff_case_1_pq_log.txt --output-dir D:\dvv_uploads\EZ\log_outputs\20250728_182022
2025-07-28 18:20:27,308 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753752027_all_parameters_comparison_grid.png
2025-07-28 18:20:27,326 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-28 18:39:31,591 - main - INFO - Processing upload: TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt (61.4 KB) for user Ethan
2025-07-28 18:39:31,598 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_183931_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt for user Ethan
2025-07-28 18:39:31,602 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250728_183931_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250728_183931
2025-07-28 18:39:32,888 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: 
2025-07-28 18:39:32,903 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-07-28 19:04:29,182 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 19:05:49,502 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 19:08:16,384 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 19:09:00,897 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 19:09:01,483 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_nonexistent_file0\nonexistent.txt
2025-07-28 19:09:01,513 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-28 19:09:01,526 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_execute_grid_comparison_p0\output
2025-07-28 19:09:01,529 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753754941_all_parameters_comparison_grid.png
2025-07-28 19:09:01,539 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-36\test_execute_firmware_log_visu0\output --no-show
2025-07-28 19:09:01,542 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753754941_L17-IC-filter_0.0416.png
2025-07-28 19:09:13,950 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-28 19:09:13,956 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-28 19:09:13,957 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\test_grid_ui_user\20250728_190913_test_log.txt for user test_grid_ui_user
2025-07-28 19:09:13,959 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-28 19:09:13,961 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/ba437421-ead1-4b58-bc2a-31766e7a5bc2 "HTTP/1.1 200 OK"
2025-07-28 19:09:13,971 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-28 19:09:13,976 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-28 19:09:13,978 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\test_firmware_ui_user\20250728_190913_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-28 19:09:13,980 - main - INFO - File processing complete: Firmware Log Visualizer requires a valid firmware log file with extractable parameters
2025-07-28 19:09:13,981 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/9db6cda2-929e-4adb-9638-d5e05ceb24ba "HTTP/1.1 200 OK"
2025-07-28 19:09:14,090 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-28 19:09:14,093 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-28 19:09:14,139 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-28 19:09:14,153 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmp2ta6lef4\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmp2ta6lef4\output
2025-07-28 19:09:14,276 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,280 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,283 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,287 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,290 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,293 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,296 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,296 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-28 19:09:14,296 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-28 19:09:14,300 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,301 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-28 19:09:14,301 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-28 19:09:14,304 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:14,307 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:45,991 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-07-28 19:09:46,338 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_nonexistent_file0\nonexistent.txt
2025-07-28 19:09:46,366 - log_processors - WARNING - File does not exist: C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_nonexistent_firmware_log0\nonexistent.txt
2025-07-28 19:09:46,378 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_execute_grid_comparison_p0\test_log.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_execute_grid_comparison_p0\output
2025-07-28 19:09:46,381 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753754986_all_parameters_comparison_grid.png
2025-07-28 19:09:46,390 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_execute_firmware_log_visu0\L17-IC-filter_0.0416.txt --output C:\Users\<USER>\AppData\Local\Temp\pytest-of-zqli\pytest-37\test_execute_firmware_log_visu0\output --no-show
2025-07-28 19:09:46,393 - log_processors - INFO - Copied L17-IC-filter_0.0416.png to static directory: /static/images/firmware_log_visualizer/1753754986_L17-IC-filter_0.0416.png
2025-07-28 19:09:58,768 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-28 19:09:58,773 - main - INFO - Processing upload: test_log.txt (257.0 B) for user test_grid_ui_user
2025-07-28 19:09:58,774 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\test_grid_ui_user\20250728_190958_test_log.txt for user test_grid_ui_user
2025-07-28 19:09:58,779 - main - INFO - File processing complete: Grid comparison visualization generated successfully
2025-07-28 19:09:58,781 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/7da3c2f2-20b0-4596-86a6-8f13ef10bde1 "HTTP/1.1 200 OK"
2025-07-28 19:09:58,793 - httpx - INFO - HTTP Request: POST http://testserver/register "HTTP/1.1 200 OK"
2025-07-28 19:09:58,798 - main - INFO - Processing upload: L17-IC-filter_0.0416.txt (167.0 B) for user test_firmware_ui_user
2025-07-28 19:09:58,800 - main - INFO - Processing log file D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads\test_firmware_ui_user\20250728_190958_L17-IC-filter_0.0416.txt for user test_firmware_ui_user
2025-07-28 19:09:58,803 - main - INFO - File processing complete: Firmware Log Visualizer requires a valid firmware log file with extractable parameters
2025-07-28 19:09:58,805 - httpx - INFO - HTTP Request: POST http://testserver/api/upload/ad10614a-3b9e-4ab9-9bce-77c321c3d98d "HTTP/1.1 200 OK"
2025-07-28 19:09:59,104 - log_processors - ERROR - Unexpected error checking TC Flash log file 
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        : 'str' object has no attribute 'exists'
2025-07-28 19:09:59,108 - log_processors - ERROR - Unexpected error checking TC Flash log file This is not a TC Flash log file: 'str' object has no attribute 'exists'
2025-07-28 19:09:59,139 - log_processors - WARNING - File does not exist: nonexistent_file.txt
2025-07-28 19:09:59,149 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py C:\Users\<USER>\AppData\Local\Temp\tmpd49nxm6q\test.txt --output-dir C:\Users\<USER>\AppData\Local\Temp\tmpd49nxm6q\output
2025-07-28 19:09:59,201 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,204 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,208 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,213 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,214 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,217 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,221 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,222 - web_app_registry - INFO - Registered new application: dynamic_app
2025-07-28 19:09:59,222 - web_app_registry - ERROR - Failed to register application invalid_app: missing 'description'
2025-07-28 19:09:59,224 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,225 - web_app_registry - INFO - Enabled application: disabled_app
2025-07-28 19:09:59,225 - web_app_registry - INFO - Disabled application: test_app_1
2025-07-28 19:09:59,228 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:09:59,231 - web_app_registry - INFO - Loaded 3 web applications
2025-07-28 19:12:44,749 - main - INFO - Processing upload: TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt (61.4 KB) for user Ethan
2025-07-28 19:12:44,760 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250728_191244_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt for user Ethan
2025-07-28 19:12:44,765 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250728_191244_TC1_OTT_L15_PR_comm_fb_TV4_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250728_191244
2025-07-28 19:12:52,332 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753755172_all_parameters_comparison_grid.png
2025-07-28 19:12:52,347 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-29 10:09:52,474 - main - INFO - Processing upload: IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt (992.6 KB) for user kennywu
2025-07-29 10:09:52,476 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_100952_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt for user kennywu
2025-07-29 10:09:52,580 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_100952_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_100952 --no-show
2025-07-29 10:09:59,722 - log_processors - INFO - Copied 20250729_100952_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png to static directory: /static/images/firmware_log_visualizer/1753808999_20250729_100952_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png
2025-07-29 10:09:59,752 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 11:59:55,755 - main - INFO - Processing upload: Filter_Tamx_test1.txt (1.1 MB) for user kennywu
2025-07-29 11:59:55,757 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_115955_Filter_Tamx_test1.txt for user kennywu
2025-07-29 11:59:55,911 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_115955_Filter_Tamx_test1.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_115955 --no-show
2025-07-29 12:00:02,472 - log_processors - INFO - Copied 20250729_115955_Filter_Tamx_test1.png to static directory: /static/images/firmware_log_visualizer/1753815602_20250729_115955_Filter_Tamx_test1.png
2025-07-29 12:00:02,493 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 12:45:34,622 - main - INFO - Processing upload: Filter_Tamx_test1.txt (1.1 MB) for user kennywu
2025-07-29 12:45:34,624 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_124534_Filter_Tamx_test1.txt for user kennywu
2025-07-29 12:45:34,742 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_124534_Filter_Tamx_test1.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_124534 --no-show
2025-07-29 12:45:41,063 - log_processors - INFO - Copied 20250729_124534_Filter_Tamx_test1.png to static directory: /static/images/firmware_log_visualizer/1753818341_20250729_124534_Filter_Tamx_test1.png
2025-07-29 12:45:41,105 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 12:46:15,470 - main - INFO - Processing upload: Filter_Tamx_test2.txt (1.1 MB) for user kennywu
2025-07-29 12:46:15,471 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_124615_Filter_Tamx_test2.txt for user kennywu
2025-07-29 12:46:15,584 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_124615_Filter_Tamx_test2.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_124615 --no-show
2025-07-29 12:46:21,244 - log_processors - INFO - Copied 20250729_124615_Filter_Tamx_test2.png to static directory: /static/images/firmware_log_visualizer/1753818381_20250729_124615_Filter_Tamx_test2.png
2025-07-29 12:46:21,262 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 12:46:58,868 - main - INFO - Processing upload: Filter_Tamx_test3.txt (1.2 MB) for user kennywu
2025-07-29 12:46:58,870 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_124658_Filter_Tamx_test3.txt for user kennywu
2025-07-29 12:46:58,981 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_124658_Filter_Tamx_test3.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_124658 --no-show
2025-07-29 12:47:04,347 - log_processors - INFO - Copied 20250729_124658_Filter_Tamx_test3.png to static directory: /static/images/firmware_log_visualizer/1753818424_20250729_124658_Filter_Tamx_test3.png
2025-07-29 12:47:04,372 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 12:47:07,973 - main - INFO - Processing upload: Filter_Tamx_test3.txt (1.2 MB) for user kennywu
2025-07-29 12:47:07,975 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_124707_Filter_Tamx_test3.txt for user kennywu
2025-07-29 12:47:08,095 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_124707_Filter_Tamx_test3.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_124708 --no-show
2025-07-29 12:47:14,526 - log_processors - INFO - Copied 20250729_124707_Filter_Tamx_test3.png to static directory: /static/images/firmware_log_visualizer/1753818434_20250729_124707_Filter_Tamx_test3.png
2025-07-29 12:47:14,551 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 12:47:37,794 - main - INFO - Processing upload: Filter_Tamx_test4.txt (1.2 MB) for user kennywu
2025-07-29 12:47:37,796 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_124737_Filter_Tamx_test4.txt for user kennywu
2025-07-29 12:47:37,906 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_124737_Filter_Tamx_test4.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_124737 --no-show
2025-07-29 12:47:43,571 - log_processors - INFO - Copied 20250729_124737_Filter_Tamx_test4.png to static directory: /static/images/firmware_log_visualizer/1753818463_20250729_124737_Filter_Tamx_test4.png
2025-07-29 12:47:43,592 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 13:42:39,203 - main - INFO - Processing upload: Filter_Tamx_0.5_0.5_test5.txt (1.2 MB) for user kennywu
2025-07-29 13:42:39,237 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_134239_Filter_Tamx_0.5_0.5_test5.txt for user kennywu
2025-07-29 13:42:39,558 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_134239_Filter_Tamx_0.5_0.5_test5.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_134239 --no-show
2025-07-29 13:42:49,475 - log_processors - INFO - Copied 20250729_134239_Filter_Tamx_0.5_0.5_test5.png to static directory: /static/images/firmware_log_visualizer/1753821769_20250729_134239_Filter_Tamx_0.5_0.5_test5.png
2025-07-29 13:42:49,496 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 13:42:52,576 - main - INFO - Processing upload: Filter_Tamx_0.5_0.5_test5.txt (1.2 MB) for user kennywu
2025-07-29 13:42:52,583 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250729_134252_Filter_Tamx_0.5_0.5_test5.txt for user kennywu
2025-07-29 13:42:52,694 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250729_134252_Filter_Tamx_0.5_0.5_test5.txt --output D:\dvv_uploads\kennywu\log_outputs\20250729_134252 --no-show
2025-07-29 13:42:58,142 - log_processors - INFO - Copied 20250729_134252_Filter_Tamx_0.5_0.5_test5.png to static directory: /static/images/firmware_log_visualizer/1753821778_20250729_134252_Filter_Tamx_0.5_0.5_test5.png
2025-07-29 13:42:58,167 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 14:07:38,217 - main - INFO - Processing upload: pq_log_beta5.txt (188.3 KB) for user cshiv
2025-07-29 14:07:38,217 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250729_140738_pq_log_beta5.txt for user cshiv
2025-07-29 14:07:38,229 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250729_140738_pq_log_beta5.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250729_140738
2025-07-29 14:07:44,030 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753823264_all_parameters_comparison_grid.png
2025-07-29 14:07:44,052 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-29 14:37:21,714 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-29 14:37:21,720 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250729_143721_pq_log.txt for user cshiv
2025-07-29 14:37:21,723 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250729_143721_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250729_143721
2025-07-29 14:37:27,011 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753825047_all_parameters_comparison_grid.png
2025-07-29 14:37:27,029 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-29 15:16:03,016 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-29 15:16:03,052 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250729_151603_pq_log.txt for user cshiv
2025-07-29 15:16:03,143 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250729_151603_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250729_151603
2025-07-29 15:16:14,057 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753827374_all_parameters_comparison_grid.png
2025-07-29 15:16:14,082 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-29 15:22:28,548 - main - INFO - Processing upload: Filter_Tamx_0.5_0.5_test5.txt (1.2 MB) for user Ethan
2025-07-29 15:22:28,550 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250729_152228_Filter_Tamx_0.5_0.5_test5.txt for user Ethan
2025-07-29 15:22:28,669 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250729_152228_Filter_Tamx_0.5_0.5_test5.txt --output D:\dvv_uploads\Ethan\log_outputs\20250729_152228 --no-show
2025-07-29 15:22:35,471 - log_processors - INFO - Copied 20250729_152228_Filter_Tamx_0.5_0.5_test5.png to static directory: /static/images/firmware_log_visualizer/1753827755_20250729_152228_Filter_Tamx_0.5_0.5_test5.png
2025-07-29 15:22:35,493 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-29 15:30:29,888 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-29 15:30:29,889 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250729_153029_pq_log.txt for user cshiv
2025-07-29 15:30:29,896 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250729_153029_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250729_153029
2025-07-29 15:30:35,779 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753828235_all_parameters_comparison_grid.png
2025-07-29 15:30:35,803 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-29 16:07:24,096 - main - INFO - Processing upload: 481114_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt (992.6 KB) for user Ethan
2025-07-29 16:07:24,098 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250729_160724_481114_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt for user Ethan
2025-07-29 16:07:24,208 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250729_160724_481114_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt --output D:\dvv_uploads\Ethan\log_outputs\20250729_160724 --no-show
2025-07-29 16:07:30,188 - log_processors - INFO - Copied 20250729_160724_481114_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png to static directory: /static/images/firmware_log_visualizer/1753830450_20250729_160724_481114_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png
2025-07-29 16:07:30,211 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-30 17:10:57,887 - main - INFO - Processing upload: pq_log.txt (91.5 KB) for user cshiv
2025-07-30 17:10:57,892 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250730_171057_pq_log.txt for user cshiv
2025-07-30 17:10:57,899 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250730_171057_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250730_171057
2025-07-30 17:11:03,440 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753920663_all_parameters_comparison_grid.png
2025-07-30 17:11:03,458 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-31 14:14:06,101 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_0.01_L15L17.txt (86.7 KB) for user shiv
2025-07-31 14:14:06,102 - main - INFO - Processing log file D:\dvv_uploads\shiv\20250731_141406_DVSDK_7353_IC10_filter_0.01_L15L17.txt for user shiv
2025-07-31 14:14:06,120 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\shiv\20250731_141406_DVSDK_7353_IC10_filter_0.01_L15L17.txt --output D:\dvv_uploads\shiv\log_outputs\20250731_141406 --no-show
2025-07-31 14:14:12,756 - log_processors - INFO - Copied 20250731_141406_DVSDK_7353_IC10_filter_0.01_L15L17.png to static directory: /static/images/firmware_log_visualizer/1753996452_20250731_141406_DVSDK_7353_IC10_filter_0.01_L15L17.png
2025-07-31 14:14:12,776 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 14:14:56,907 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_1_L15L17.txt (149.6 KB) for user shiv
2025-07-31 14:14:56,909 - main - INFO - Processing log file D:\dvv_uploads\shiv\20250731_141456_DVSDK_7353_IC10_filter_1_L15L17.txt for user shiv
2025-07-31 14:14:56,928 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\shiv\20250731_141456_DVSDK_7353_IC10_filter_1_L15L17.txt --output D:\dvv_uploads\shiv\log_outputs\20250731_141456 --no-show
2025-07-31 14:15:02,349 - log_processors - INFO - Copied 20250731_141456_DVSDK_7353_IC10_filter_1_L15L17.png to static directory: /static/images/firmware_log_visualizer/1753996502_20250731_141456_DVSDK_7353_IC10_filter_1_L15L17.png
2025-07-31 14:15:02,369 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 15:07:01,838 - main - INFO - Processing upload: pq_log.txt (392.3 KB) for user cshiv
2025-07-31 15:07:01,839 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250731_150701_pq_log.txt for user cshiv
2025-07-31 15:07:01,843 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250731_150701_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250731_150701
2025-07-31 15:07:07,311 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1753999627_all_parameters_comparison_grid.png
2025-07-31 15:07:07,349 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-31 15:58:29,939 - main - INFO - Processing upload: IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt (992.6 KB) for user kennywu
2025-07-31 15:58:29,942 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250731_155829_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt for user kennywu
2025-07-31 15:58:30,050 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250731_155829_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.txt --output D:\dvv_uploads\kennywu\log_outputs\20250731_155829 --no-show
2025-07-31 15:58:36,287 - log_processors - INFO - Copied 20250731_155829_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png to static directory: /static/images/firmware_log_visualizer/1754002716_20250731_155829_IC5-filter-1-0.01-0.0416-0.1-0.5-0.7-0.8-1.png
2025-07-31 15:58:36,305 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 16:01:38,394 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_0.01_L15L17.txt (86.7 KB) for user kennywu
2025-07-31 16:01:38,398 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250731_160138_DVSDK_7353_IC10_filter_0.01_L15L17.txt for user kennywu
2025-07-31 16:01:38,414 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250731_160138_DVSDK_7353_IC10_filter_0.01_L15L17.txt --output D:\dvv_uploads\kennywu\log_outputs\20250731_160138 --no-show
2025-07-31 16:01:43,830 - log_processors - INFO - Copied 20250731_160138_DVSDK_7353_IC10_filter_0.01_L15L17.png to static directory: /static/images/firmware_log_visualizer/1754002903_20250731_160138_DVSDK_7353_IC10_filter_0.01_L15L17.png
2025-07-31 16:01:43,847 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 16:02:55,344 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_1_L15L17.txt (149.6 KB) for user kennywu
2025-07-31 16:02:55,346 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250731_160255_DVSDK_7353_IC10_filter_1_L15L17.txt for user kennywu
2025-07-31 16:02:55,363 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250731_160255_DVSDK_7353_IC10_filter_1_L15L17.txt --output D:\dvv_uploads\kennywu\log_outputs\20250731_160255 --no-show
2025-07-31 16:03:01,248 - log_processors - INFO - Copied 20250731_160255_DVSDK_7353_IC10_filter_1_L15L17.png to static directory: /static/images/firmware_log_visualizer/1754002981_20250731_160255_DVSDK_7353_IC10_filter_1_L15L17.png
2025-07-31 16:03:01,264 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 16:03:54,499 - main - INFO - Processing upload: pq_log.txt (392.3 KB) for user cshiv
2025-07-31 16:03:54,501 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250731_160354_pq_log.txt for user cshiv
2025-07-31 16:03:54,505 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250731_160354_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250731_160354
2025-07-31 16:03:59,928 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754003039_all_parameters_comparison_grid.png
2025-07-31 16:03:59,951 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-07-31 16:05:32,962 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_0.01_L15L17.txt (86.7 KB) for user kennywu
2025-07-31 16:05:32,963 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250731_160532_DVSDK_7353_IC10_filter_0.01_L15L17.txt for user kennywu
2025-07-31 16:05:32,977 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250731_160532_DVSDK_7353_IC10_filter_0.01_L15L17.txt --output D:\dvv_uploads\kennywu\log_outputs\20250731_160532 --no-show
2025-07-31 16:05:38,318 - log_processors - INFO - Copied 20250731_160532_DVSDK_7353_IC10_filter_0.01_L15L17.png to static directory: /static/images/firmware_log_visualizer/1754003138_20250731_160532_DVSDK_7353_IC10_filter_0.01_L15L17.png
2025-07-31 16:05:38,337 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-07-31 16:05:40,835 - main - INFO - Processing upload: DVSDK_7353_IC10_filter_0.01_L15L17.txt (86.7 KB) for user kennywu
2025-07-31 16:05:40,836 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250731_160540_DVSDK_7353_IC10_filter_0.01_L15L17.txt for user kennywu
2025-07-31 16:05:40,850 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250731_160540_DVSDK_7353_IC10_filter_0.01_L15L17.txt --output D:\dvv_uploads\kennywu\log_outputs\20250731_160540 --no-show
2025-07-31 16:05:48,015 - log_processors - INFO - Copied 20250731_160540_DVSDK_7353_IC10_filter_0.01_L15L17.png to static directory: /static/images/firmware_log_visualizer/1754003148_20250731_160540_DVSDK_7353_IC10_filter_0.01_L15L17.png
2025-07-31 16:05:48,032 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-01 12:16:36,009 - main - INFO - Processing upload: pq_log.txt (188.3 KB) for user cshiv
2025-08-01 12:16:36,015 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_121636_pq_log.txt for user cshiv
2025-08-01 12:16:36,020 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_121636_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_121636
2025-08-01 12:16:42,292 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754075802_all_parameters_comparison_grid.png
2025-08-01 12:16:42,319 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-01 12:50:32,816 - main - INFO - Processing upload: pq_log.txt (188.3 KB) for user cshiv
2025-08-01 12:50:32,826 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_125032_pq_log.txt for user cshiv
2025-08-01 12:50:32,830 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_125032_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_125032
2025-08-01 12:50:42,707 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754077842_all_parameters_comparison_grid.png
2025-08-01 12:50:42,828 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-01 13:21:53,574 - main - INFO - Processing upload: pq_log.txt (188.3 KB) for user cshiv
2025-08-01 13:21:53,584 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_132153_pq_log.txt for user cshiv
2025-08-01 13:21:53,589 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_132153_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_132153
2025-08-01 13:22:00,677 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754079720_all_parameters_comparison_grid.png
2025-08-01 13:22:00,697 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-01 13:44:54,358 - main - INFO - Processing upload: pq_log.txt (188.3 KB) for user cshiv
2025-08-01 13:44:54,359 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_134454_pq_log.txt for user cshiv
2025-08-01 13:44:54,364 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_134454_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_134454
2025-08-01 13:45:01,468 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754081101_all_parameters_comparison_grid.png
2025-08-01 13:45:01,489 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-01 15:32:48,022 - main - INFO - Processing upload: L17L15-660_795-1027-IC10-011-1-0.9.txt (483.2 KB) for user kennywu
2025-08-01 15:32:48,029 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250801_153248_L17L15-660_795-1027-IC10-011-1-0.9.txt for user kennywu
2025-08-01 15:32:48,102 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250801_153248_L17L15-660_795-1027-IC10-011-1-0.9.txt --output D:\dvv_uploads\kennywu\log_outputs\20250801_153248 --no-show
2025-08-01 15:32:54,943 - log_processors - INFO - Copied 20250801_153248_L17L15-660_795-1027-IC10-011-1-0.9.png to static directory: /static/images/firmware_log_visualizer/1754087574_20250801_153248_L17L15-660_795-1027-IC10-011-1-0.9.png
2025-08-01 15:32:54,974 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-01 15:33:15,561 - main - INFO - Processing upload: L17L15-660_795-1027-IC10-no-blending.txt (524.0 KB) for user kennywu
2025-08-01 15:33:15,562 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250801_153315_L17L15-660_795-1027-IC10-no-blending.txt for user kennywu
2025-08-01 15:33:15,622 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250801_153315_L17L15-660_795-1027-IC10-no-blending.txt --output D:\dvv_uploads\kennywu\log_outputs\20250801_153315 --no-show
2025-08-01 15:33:21,131 - log_processors - INFO - Copied 20250801_153315_L17L15-660_795-1027-IC10-no-blending.png to static directory: /static/images/firmware_log_visualizer/1754087601_20250801_153315_L17L15-660_795-1027-IC10-no-blending.png
2025-08-01 15:33:21,150 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-01 16:07:22,232 - main - INFO - Processing upload: pq_log_sim2.txt (188.3 KB) for user cshiv
2025-08-01 16:07:22,236 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_160722_pq_log_sim2.txt for user cshiv
2025-08-01 16:07:22,242 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_160722_pq_log_sim2.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_160722
2025-08-01 16:07:28,421 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754089648_all_parameters_comparison_grid.png
2025-08-01 16:07:28,444 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-01 16:07:49,084 - main - INFO - Processing upload: pq_log_sim1.txt (188.3 KB) for user cshiv
2025-08-01 16:07:49,084 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250801_160749_pq_log_sim1.txt for user cshiv
2025-08-01 16:07:49,084 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250801_160749_pq_log_sim1.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250801_160749
2025-08-01 16:07:54,542 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754089674_all_parameters_comparison_grid.png
2025-08-01 16:07:54,552 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-04 13:21:12,504 - main - INFO - Processing upload: Senev-T730-500-900-IC5-011-1-1.txt (681.1 KB) for user kennywu
2025-08-04 13:21:12,514 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250804_132112_Senev-T730-500-900-IC5-011-1-1.txt for user kennywu
2025-08-04 13:21:12,595 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250804_132112_Senev-T730-500-900-IC5-011-1-1.txt --output D:\dvv_uploads\kennywu\log_outputs\20250804_132112 --no-show
2025-08-04 13:21:19,608 - log_processors - INFO - Copied 20250804_132112_Senev-T730-500-900-IC5-011-1-1.png to static directory: /static/images/firmware_log_visualizer/1754338879_20250804_132112_Senev-T730-500-900-IC5-011-1-1.png
2025-08-04 13:21:19,634 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-04 13:21:38,194 - main - INFO - Processing upload: Senev-T730-795-900-IC5-011-0.5-0.5.txt (699.8 KB) for user kennywu
2025-08-04 13:21:38,195 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250804_132138_Senev-T730-795-900-IC5-011-0.5-0.5.txt for user kennywu
2025-08-04 13:21:38,266 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250804_132138_Senev-T730-795-900-IC5-011-0.5-0.5.txt --output D:\dvv_uploads\kennywu\log_outputs\20250804_132138 --no-show
2025-08-04 13:21:43,711 - log_processors - INFO - Copied 20250804_132138_Senev-T730-795-900-IC5-011-0.5-0.5.png to static directory: /static/images/firmware_log_visualizer/1754338903_20250804_132138_Senev-T730-795-900-IC5-011-0.5-0.5.png
2025-08-04 13:21:43,729 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-04 13:49:30,601 - main - INFO - Processing upload: Senev-T730-450-700-IC5-000-1-0.9.txt (674.7 KB) for user kennywu
2025-08-04 13:49:30,604 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250804_134930_Senev-T730-450-700-IC5-000-1-0.9.txt for user kennywu
2025-08-04 13:49:30,674 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250804_134930_Senev-T730-450-700-IC5-000-1-0.9.txt --output D:\dvv_uploads\kennywu\log_outputs\20250804_134930 --no-show
2025-08-04 13:49:37,063 - log_processors - INFO - Copied 20250804_134930_Senev-T730-450-700-IC5-000-1-0.9.png to static directory: /static/images/firmware_log_visualizer/1754340577_20250804_134930_Senev-T730-450-700-IC5-000-1-0.9.png
2025-08-04 13:49:37,081 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-04 13:49:55,522 - main - INFO - Processing upload: Senev-T730-450-700-IC5-011-0.9-0.9.txt (670.9 KB) for user kennywu
2025-08-04 13:49:55,523 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250804_134955_Senev-T730-450-700-IC5-011-0.9-0.9.txt for user kennywu
2025-08-04 13:49:55,586 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250804_134955_Senev-T730-450-700-IC5-011-0.9-0.9.txt --output D:\dvv_uploads\kennywu\log_outputs\20250804_134955 --no-show
2025-08-04 13:50:01,040 - log_processors - INFO - Copied 20250804_134955_Senev-T730-450-700-IC5-011-0.9-0.9.png to static directory: /static/images/firmware_log_visualizer/1754340601_20250804_134955_Senev-T730-450-700-IC5-011-0.9-0.9.png
2025-08-04 13:50:01,063 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-06 13:20:48,281 - main - INFO - Processing upload: pq_log.txt (188.3 KB) for user cshiv
2025-08-06 13:20:48,284 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250806_132048_pq_log.txt for user cshiv
2025-08-06 13:20:48,290 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\cshiv\20250806_132048_pq_log.txt --output-dir D:\dvv_uploads\cshiv\log_outputs\20250806_132048
2025-08-06 13:20:54,777 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1754511654_all_parameters_comparison_grid.png
2025-08-06 13:20:54,810 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-06 16:31:39,099 - main - INFO - Processing upload: MarcoPolo_S0E2_trim1_HW714_L17L15.txt (489.9 KB) for user kennywu
2025-08-06 16:31:39,102 - main - INFO - Processing log file D:\dvv_uploads\kennywu\20250806_163139_MarcoPolo_S0E2_trim1_HW714_L17L15.txt for user kennywu
2025-08-06 16:31:39,160 - log_processors - INFO - Executing command: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\.venv\Scripts\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\kennywu\20250806_163139_MarcoPolo_S0E2_trim1_HW714_L17L15.txt --output D:\dvv_uploads\kennywu\log_outputs\20250806_163139 --no-show
2025-08-06 16:31:45,819 - log_processors - INFO - Copied 20250806_163139_MarcoPolo_S0E2_trim1_HW714_L17L15.png to static directory: /static/images/firmware_log_visualizer/1754523105_20250806_163139_MarcoPolo_S0E2_trim1_HW714_L17L15.png
2025-08-06 16:31:45,839 - main - INFO - File processing complete: Firmware log visualization generated successfully. Created 2 files.
2025-08-12 18:15:27,091 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
2025-08-12 18:27:09,431 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-13 16:07:47,485 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-13 16:07:47,489 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_160747_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-13 16:07:47,492 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:07:47,493 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_160747_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250813_160747_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-13 16:07:47,643 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-08-13 16:07:47,643 - main - INFO - External tool found 6 validation warnings
2025-08-13 16:07:47,643 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-08-13 16:07:47,656 - main - INFO - File processing complete: Analysis complete. Found 6 validation warning(s).
2025-08-13 16:09:03,858 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-13 16:09:03,859 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_160903_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-13 16:09:03,861 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:09:03,862 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_160903_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250813_160903_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-13 16:09:03,900 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-08-13 16:09:03,900 - main - INFO - External tool found 5 validation warnings
2025-08-13 16:09:03,901 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-08-13 16:09:03,917 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-08-13 16:09:13,294 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-13 16:09:13,295 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_160913_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-13 16:09:13,297 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:09:13,297 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_160913_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250813_160913_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-13 16:09:13,337 - main - INFO - External tool found 5 unique validation warnings (after deduplication)
2025-08-13 16:09:13,338 - main - INFO - External tool found 5 validation warnings
2025-08-13 16:09:13,338 - main - INFO - Analysis complete. Found 5 validation warning(s).
2025-08-13 16:09:13,354 - main - INFO - File processing complete: Analysis complete. Found 5 validation warning(s).
2025-08-13 16:11:34,647 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg (4.7 KB) for user Aman
2025-08-13 16:11:34,649 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161134_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-13 16:11:34,651 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:11:34,651 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161134_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250813_161134_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-13 16:11:34,694 - main - INFO - External tool found 8 unique validation warnings (after deduplication)
2025-08-13 16:11:34,694 - main - INFO - External tool found 8 validation warnings
2025-08-13 16:11:34,694 - main - INFO - Analysis complete. Found 8 validation warning(s).
2025-08-13 16:11:34,704 - main - INFO - File processing complete: Analysis complete. Found 8 validation warning(s).
2025-08-13 16:12:17,767 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3.cfg (2.1 KB) for user Aman
2025-08-13 16:12:17,768 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161217_Sample_TV_IDK_5.0_GD_int3.cfg for user Aman
2025-08-13 16:12:17,770 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:12:17,771 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161217_Sample_TV_IDK_5.0_GD_int3.cfg -b D:\dvv_uploads\Aman\20250813_161217_Sample_TV_IDK_5.0_GD_int3.bin
2025-08-13 16:12:17,808 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-08-13 16:12:17,808 - main - INFO - External tool found 0 validation warnings
2025-08-13 16:12:17,809 - main - INFO - Analysis complete. No issues found.
2025-08-13 16:12:17,819 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-08-13 16:14:00,530 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg (4.7 KB) for user Aman
2025-08-13 16:14:00,531 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161400_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-13 16:14:00,534 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:14:00,534 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161400_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250813_161400_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-13 16:14:00,577 - main - INFO - External tool found 8 unique validation warnings (after deduplication)
2025-08-13 16:14:00,577 - main - INFO - External tool found 8 validation warnings
2025-08-13 16:14:00,578 - main - INFO - Analysis complete. Found 8 validation warning(s).
2025-08-13 16:14:00,594 - main - INFO - File processing complete: Analysis complete. Found 8 validation warning(s).
2025-08-13 16:14:48,878 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg (4.7 KB) for user Aman
2025-08-13 16:14:48,883 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-13 16:14:48,885 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:14:48,885 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-13 16:14:48,923 - main - INFO - External tool found 8 unique validation warnings (after deduplication)
2025-08-13 16:14:48,923 - main - INFO - External tool found 8 validation warnings
2025-08-13 16:14:48,923 - main - INFO - Analysis complete. Found 8 validation warning(s).
2025-08-13 16:14:48,936 - main - INFO - File processing complete: Analysis complete. Found 8 validation warning(s).
2025-08-13 16:15:45,403 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-13 16:15:45,406 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:15:45,407 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250813_161448_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-13 16:15:45,444 - main - INFO - External tool found 8 unique validation warnings (after deduplication)
2025-08-13 16:15:45,444 - main - INFO - External tool found 8 validation warnings
2025-08-13 16:15:45,444 - main - INFO - Analysis complete. Found 8 validation warning(s).
2025-08-13 16:17:58,447 - main - INFO - Processing upload: Sample_TV_IDK_5.0_int3.cfg (1.7 KB) for user Aman
2025-08-13 16:17:58,448 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161758_Sample_TV_IDK_5.0_int3.cfg for user Aman
2025-08-13 16:17:58,450 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:17:58,450 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161758_Sample_TV_IDK_5.0_int3.cfg -b D:\dvv_uploads\Aman\20250813_161758_Sample_TV_IDK_5.0_int3.bin
2025-08-13 16:17:58,487 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-08-13 16:17:58,487 - main - INFO - External tool found 0 validation warnings
2025-08-13 16:17:58,487 - main - INFO - Analysis complete. No issues found.
2025-08-13 16:17:58,502 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-08-13 16:18:20,443 - main - INFO - Processing upload: Sample_TV_IDK_5.0_LS_PD_GD_int3.cfg (3.7 KB) for user Aman
2025-08-13 16:18:20,445 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161820_Sample_TV_IDK_5.0_LS_PD_GD_int3.cfg for user Aman
2025-08-13 16:18:20,447 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:18:20,447 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161820_Sample_TV_IDK_5.0_LS_PD_GD_int3.cfg -b D:\dvv_uploads\Aman\20250813_161820_Sample_TV_IDK_5.0_LS_PD_GD_int3.bin
2025-08-13 16:18:20,486 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-08-13 16:18:20,486 - main - INFO - External tool found 0 validation warnings
2025-08-13 16:18:20,487 - main - INFO - Analysis complete. No issues found.
2025-08-13 16:18:20,500 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-08-13 16:19:24,719 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg (4.7 KB) for user Aman
2025-08-13 16:19:24,721 - main - INFO - Processing file D:\dvv_uploads\Aman\20250813_161924_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-13 16:19:24,723 - main - INFO - Running external Dolby Vision validation tool
2025-08-13 16:19:24,723 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250813_161924_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250813_161924_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-13 16:19:24,761 - main - INFO - External tool found 7 unique validation warnings (after deduplication)
2025-08-13 16:19:24,761 - main - INFO - External tool found 7 validation warnings
2025-08-13 16:19:24,761 - main - INFO - Analysis complete. Found 7 validation warning(s).
2025-08-13 16:19:24,778 - main - INFO - File processing complete: Analysis complete. Found 7 validation warning(s).
2025-08-14 09:13:18,829 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (3.9 KB) for user Aman
2025-08-14 09:13:18,832 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_091318_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:13:18,837 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:13:18,840 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_091318_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_091318_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:13:18,922 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:13:18,922 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:13:18,922 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:13:18,942 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:14:04,121 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (4.2 KB) for user Aman
2025-08-14 09:14:04,121 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_091404_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:14:04,132 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:14:04,132 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_091404_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_091404_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:14:04,173 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:14:04,173 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:14:04,173 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:14:04,193 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:14:39,684 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (4.3 KB) for user Aman
2025-08-14 09:14:39,684 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_091439_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:14:39,684 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:14:39,684 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_091439_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_091439_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:14:39,731 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:14:39,731 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:14:39,731 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:14:39,741 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:15:20,017 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (4.7 KB) for user Aman
2025-08-14 09:15:20,017 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_091520_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:15:20,029 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:15:20,029 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_091520_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_091520_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:15:20,069 - main - INFO - External tool found 2 unique validation warnings (after deduplication)
2025-08-14 09:15:20,069 - main - INFO - External tool found 2 validation warnings
2025-08-14 09:15:20,069 - main - INFO - Analysis complete. Found 2 validation warning(s).
2025-08-14 09:15:20,090 - main - INFO - File processing complete: Analysis complete. Found 2 validation warning(s).
2025-08-14 09:16:45,603 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (4.7 KB) for user Aman
2025-08-14 09:16:45,612 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_091645_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:16:45,612 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:16:45,612 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_091645_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_091645_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:16:45,661 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:16:45,661 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:16:45,661 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:16:45,682 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:20:35,686 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (3.2 KB) for user Aman
2025-08-14 09:20:35,686 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_092035_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:20:35,686 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:20:35,695 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_092035_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_092035_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:20:35,728 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:20:35,728 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:20:35,728 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:20:35,748 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:21:30,843 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_int3 - Copy.cfg (3.6 KB) for user Aman
2025-08-14 09:21:30,843 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_092130_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg for user Aman
2025-08-14 09:21:30,853 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:21:30,853 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_092130_Sample_TV_IDK_5.0_GD_int3 - Copy.cfg -b D:\dvv_uploads\Aman\20250814_092130_Sample_TV_IDK_5.0_GD_int3 - Copy.bin
2025-08-14 09:21:30,895 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:21:30,895 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:21:30,895 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:21:30,916 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:26:00,255 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-14 09:26:00,258 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_092600_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-14 09:26:00,259 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:26:00,260 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_092600_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250814_092600_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-14 09:26:00,302 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:26:00,302 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:26:00,302 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:26:00,313 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:26:48,797 - main - INFO - Processing upload: 2025_08_04_10_20_38_PQ.cfg (6.2 KB) for user Aman
2025-08-14 09:26:48,798 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_092648_2025_08_04_10_20_38_PQ.cfg for user Aman
2025-08-14 09:26:48,801 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:26:48,801 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_092648_2025_08_04_10_20_38_PQ.cfg -b D:\dvv_uploads\Aman\20250814_092648_2025_08_04_10_20_38_PQ.bin
2025-08-14 09:26:48,848 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:26:48,849 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:26:48,849 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:26:48,868 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:29:14,619 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-14 09:29:14,622 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_092914_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-14 09:29:14,625 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:29:14,626 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_092914_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250814_092914_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-14 09:29:14,668 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:29:14,669 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:29:14,669 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:29:14,680 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:31:16,344 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg (3.6 KB) for user Aman
2025-08-14 09:31:16,351 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_093116_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg for user Aman
2025-08-14 09:31:16,354 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:31:16,355 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_093116_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.cfg -b D:\dvv_uploads\Aman\20250814_093116_Sample_TV_IDK_5.0_GD_L15_L16_L18_int3.bin
2025-08-14 09:31:16,418 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:31:16,419 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:31:16,420 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:31:16,445 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 09:31:25,020 - main - INFO - Processing upload: Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg (4.7 KB) for user Aman
2025-08-14 09:31:25,023 - main - INFO - Processing file D:\dvv_uploads\Aman\20250814_093125_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg for user Aman
2025-08-14 09:31:25,025 - main - INFO - Running external Dolby Vision validation tool
2025-08-14 09:31:25,026 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\Aman\20250814_093125_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.cfg -b D:\dvv_uploads\Aman\20250814_093125_Sample_TV_IDK_5.0_GD_L15_L16_L17_L18_LS_int3.bin
2025-08-14 09:31:25,063 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-14 09:31:25,063 - main - INFO - External tool found 1 validation warnings
2025-08-14 09:31:25,063 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-14 09:31:25,080 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-14 12:45:48,044 - main - INFO - Processing upload: pq_log.txt (39.2 KB) for user cshiv
2025-08-14 12:45:48,047 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250814_124548_pq_log.txt for user cshiv
2025-08-14 12:45:48,072 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-14 12:45:52,204 - main - INFO - Processing upload: pq_log.txt (39.2 KB) for user cshiv
2025-08-14 12:45:52,210 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250814_124552_pq_log.txt for user cshiv
2025-08-14 12:45:52,227 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-14 12:46:12,938 - main - INFO - Processing upload: pq_log.txt (39.2 KB) for user cshiv
2025-08-14 12:46:13,037 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250814_124613_pq_log.txt for user cshiv
2025-08-14 12:46:13,281 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-14 13:03:44,638 - main - INFO - Processing upload: pq_log.txt (2.6 KB) for user cshiv
2025-08-14 13:03:44,638 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250814_130344_pq_log.txt for user cshiv
2025-08-14 13:03:44,670 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-14 13:04:28,166 - main - INFO - Processing upload: pq_log.txt (2.6 KB) for user cshiv
2025-08-14 13:04:28,171 - main - INFO - Processing log file D:\dvv_uploads\cshiv\20250814_130428_pq_log.txt for user cshiv
2025-08-14 13:04:28,176 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-15 14:23:51,196 - main - INFO - Processing upload: disable-l15l17IC.cfg (6.3 KB) for user EZ
2025-08-15 14:23:51,200 - main - INFO - Processing file D:\dvv_uploads\EZ\20250815_142351_disable-l15l17IC.cfg for user EZ
2025-08-15 14:23:51,203 - main - INFO - Running external Dolby Vision validation tool
2025-08-15 14:23:51,206 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\EZ\20250815_142351_disable-l15l17IC.cfg -b D:\dvv_uploads\EZ\20250815_142351_disable-l15l17IC.bin
2025-08-15 14:23:51,277 - main - INFO - External tool found 6 unique validation warnings (after deduplication)
2025-08-15 14:23:51,278 - main - INFO - External tool found 6 validation warnings
2025-08-15 14:23:51,278 - main - INFO - Analysis complete. Found 6 validation warning(s).
2025-08-15 14:23:51,298 - main - INFO - File processing complete: Analysis complete. Found 6 validation warning(s).
2025-08-16 18:57:55,861 - main - INFO - Processing upload: DOLBY_FACTORY_Beta7.cfg (6.0 KB) for user EZ
2025-08-16 18:57:55,964 - main - INFO - Processing file D:\dvv_uploads\EZ\20250816_185755_DOLBY_FACTORY_Beta7.cfg for user EZ
2025-08-16 18:57:56,060 - main - INFO - Running external Dolby Vision validation tool
2025-08-16 18:57:56,148 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\EZ\20250816_185755_DOLBY_FACTORY_Beta7.cfg -b D:\dvv_uploads\EZ\20250816_185755_DOLBY_FACTORY_Beta7.bin
2025-08-16 18:57:56,299 - main - INFO - External tool found 1 unique validation warnings (after deduplication)
2025-08-16 18:57:56,303 - main - INFO - External tool found 1 validation warnings
2025-08-16 18:57:56,303 - main - INFO - Analysis complete. Found 1 validation warning(s).
2025-08-16 18:57:56,332 - main - INFO - File processing complete: Analysis complete. Found 1 validation warning(s).
2025-08-16 18:59:07,002 - main - INFO - Processing upload: DOLBY_FACTORY_Beta7.cfg (6.0 KB) for user EZ
2025-08-16 18:59:07,005 - main - INFO - Processing file D:\dvv_uploads\EZ\20250816_185907_DOLBY_FACTORY_Beta7.cfg for user EZ
2025-08-16 18:59:07,008 - main - INFO - Running external Dolby Vision validation tool
2025-08-16 18:59:07,008 - main - INFO - Running external validation: bin\dtv_config_lib_test.exe -t D:\dvv_uploads\EZ\20250816_185907_DOLBY_FACTORY_Beta7.cfg -b D:\dvv_uploads\EZ\20250816_185907_DOLBY_FACTORY_Beta7.bin
2025-08-16 18:59:07,047 - main - INFO - External tool found 0 unique validation warnings (after deduplication)
2025-08-16 18:59:07,048 - main - INFO - External tool found 0 validation warnings
2025-08-16 18:59:07,048 - main - INFO - Analysis complete. No issues found.
2025-08-16 18:59:07,058 - main - INFO - File processing complete: Analysis complete. No issues found.
2025-08-16 18:59:31,656 - main - INFO - Processing upload: TC_Blending_issue_3_pq_log.txt (192.1 KB) for user EZ
2025-08-16 18:59:31,659 - main - INFO - Processing log file D:\dvv_uploads\EZ\20250816_185931_TC_Blending_issue_3_pq_log.txt for user EZ
2025-08-16 18:59:31,686 - main - INFO - File processing complete: IDK-IC In-and-Out requires a TC Flash PQ log file with Config and DM values sections
2025-08-16 18:59:50,248 - main - INFO - Processing upload: 7360_pq_log.txt (192.1 KB) for user EZ
2025-08-16 18:59:50,249 - main - INFO - Processing log file D:\dvv_uploads\EZ\20250816_185950_7360_pq_log.txt for user EZ
2025-08-16 18:59:50,259 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\EZ\20250816_185950_7360_pq_log.txt --output-dir D:\dvv_uploads\EZ\log_outputs\20250816_185950
2025-08-16 18:59:50,548 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-08-16 18:59:50,561 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-08-16 18:59:57,331 - main - INFO - Processing upload: 7360_pq_log.txt (192.1 KB) for user EZ
2025-08-16 18:59:57,332 - main - INFO - Processing log file D:\dvv_uploads\EZ\20250816_185957_7360_pq_log.txt for user EZ
2025-08-16 18:59:57,335 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\EZ\20250816_185957_7360_pq_log.txt --output-dir D:\dvv_uploads\EZ\log_outputs\20250816_185957
2025-08-16 18:59:57,422 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-08-16 18:59:57,445 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-08-16 19:00:06,232 - main - INFO - Processing upload: TC_Blending_issue_scenario_1_luma_0.3_pq_log.txt (192.1 KB) for user EZ
2025-08-16 19:00:06,234 - main - INFO - Processing log file D:\dvv_uploads\EZ\20250816_190006_TC_Blending_issue_scenario_1_luma_0.3_pq_log.txt for user EZ
2025-08-16 19:00:06,239 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\EZ\20250816_190006_TC_Blending_issue_scenario_1_luma_0.3_pq_log.txt --output-dir D:\dvv_uploads\EZ\log_outputs\20250816_190006
2025-08-16 19:00:06,328 - log_processors - ERROR - Grid comparison plotter failed with return code 1. stderr: Traceback (most recent call last):
  File "D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py", line 16, in <module>
    import matplotlib.pyplot as plt
ModuleNotFoundError: No module named 'matplotlib'

2025-08-16 19:00:06,346 - main - INFO - File processing complete: Grid comparison plotter failed with return code 1
2025-08-17 16:41:17,612 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-17 20:16:08,179 - main - INFO - Processing upload: L17-IC-filter_1.txt (356.2 KB) for user Ethan
2025-08-17 20:16:08,180 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_201608_L17-IC-filter_1.txt for user Ethan
2025-08-17 20:16:08,239 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250817_201608_L17-IC-filter_1.txt --output D:\dvv_uploads\Ethan\log_outputs\20250817_201608 --no-show
2025-08-17 20:16:10,928 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:17:14,302 - main - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-08-17 20:17:14,302 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_201714_TC_stranger_things_pq_log.txt for user Ethan
2025-08-17 20:17:14,441 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250817_201714_TC_stranger_things_pq_log.txt --output D:\dvv_uploads\Ethan\log_outputs\20250817_201714 --no-show
2025-08-17 20:17:16,146 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:19:20,766 - main - INFO - Processing upload: TC_stranger_things_pq_log.txt (93.4 KB) for user Ethan
2025-08-17 20:19:20,766 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_201920_TC_stranger_things_pq_log.txt for user Ethan
2025-08-17 20:19:20,776 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\grid_comparison_plotter.py D:\dvv_uploads\Ethan\20250817_201920_TC_stranger_things_pq_log.txt --output-dir D:\dvv_uploads\Ethan\log_outputs\20250817_201920
2025-08-17 20:19:28,987 - log_processors - INFO - Copied all_parameters_comparison_grid.png to static directory: /static/images/grid_comparison/1755487168_all_parameters_comparison_grid.png
2025-08-17 20:19:29,022 - main - INFO - File processing complete: Successfully generated grid comparison plot
2025-08-17 20:19:55,694 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-08-17 20:19:55,694 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_201955_L17-IC-filter_0.5.txt for user Ethan
2025-08-17 20:19:55,758 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py D:\dvv_uploads\Ethan\20250817_201955_L17-IC-filter_0.5.txt --output D:\dvv_uploads\Ethan\log_outputs\20250817_201955 --no-show
2025-08-17 20:19:57,658 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:36:21,233 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-17 20:36:46,954 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-08-17 20:36:46,954 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_203646_L17-IC-filter_0.5.txt for user Ethan
2025-08-17 20:36:47,005 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py -f D:\dvv_uploads\Ethan\20250817_203646_L17-IC-filter_0.5.txt -o D:\dvv_uploads\Ethan\log_outputs\20250817_203646 --no-show
2025-08-17 20:36:49,172 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:39:37,677 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-17 20:40:09,984 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-08-17 20:40:09,986 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_204009_L17-IC-filter_0.5.txt for user Ethan
2025-08-17 20:40:10,025 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py -f D:\dvv_uploads\Ethan\20250817_204009_L17-IC-filter_0.5.txt -o D:\dvv_uploads\Ethan\log_outputs\20250817_204010 --no-show
2025-08-17 20:40:11,791 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:42:19,377 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-17 20:42:40,967 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-08-17 20:42:40,968 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_204240_L17-IC-filter_0.5.txt for user Ethan
2025-08-17 20:42:41,024 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py -f D:\dvv_uploads\Ethan\20250817_204240_L17-IC-filter_0.5.txt -o D:\dvv_uploads\Ethan\log_outputs\20250817_204240 --no-show
2025-08-17 20:42:42,795 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 20:56:15,673 - main - INFO - Upload directory configured: D:\dvv_uploads
2025-08-17 20:56:32,772 - main - INFO - Processing upload: L17-IC-filter_0.5.txt (442.3 KB) for user Ethan
2025-08-17 20:56:32,772 - main - INFO - Processing log file D:\dvv_uploads\Ethan\20250817_205632_L17-IC-filter_0.5.txt for user Ethan
2025-08-17 20:56:32,824 - log_processors - INFO - Executing command: C:\Program Files\Python311\python.exe D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\bin\firmware_log_visualizer.py -f D:\dvv_uploads\Ethan\20250817_205632_L17-IC-filter_0.5.txt -o D:\dvv_uploads\Ethan\log_outputs\20250817_205632 --no-show
2025-08-17 20:56:35,120 - main - INFO - File processing complete: Firmware log visualizer failed with return code 1
2025-08-17 21:14:59,798 - main - INFO - Upload directory configured: D:\PycharmProjects\pq_tuning_config_editor\prototypes\dvv\uploads
